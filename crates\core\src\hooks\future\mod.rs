use parking_lot::Mutex;
use std::fmt;
use std::sync::Arc;
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;

#[cfg(test)]
mod tests;

use crate::hooks::effect::EffectDependencies;
use crate::hooks::with_hook_context;
use crate::panic_handler::spawn_catch_panic;

/// Represents the current state of a future operation
///
/// This enum provides a comprehensive view of the future's lifecycle,
/// similar to Promise states in JavaScript or Result types in Rust.
#[derive(Debu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum FutureState<T, E = Box<dyn std::error::Error + Send + Sync>> {
    /// The future is currently pending (not yet resolved)
    #[default]
    Pending,
    /// The future has resolved successfully with a value
    Resolved(T),
    /// The future has failed with an error
    Error(E),
}

impl<T, E> FutureState<T, E> {
    /// Returns true if the future is currently pending
    pub fn is_pending(&self) -> bool {
        matches!(self, FutureState::Pending)
    }

    /// Returns true if the future has resolved successfully
    pub fn is_resolved(&self) -> bool {
        matches!(self, FutureState::Resolved(_))
    }

    /// Returns true if the future has failed with an error
    pub fn is_error(&self) -> bool {
        matches!(self, FutureState::Error(_))
    }

    /// Returns the resolved value if available, otherwise None
    pub fn value(&self) -> Option<&T> {
        match self {
            FutureState::Resolved(value) => Some(value),
            _ => None,
        }
    }

    /// Returns the error if available, otherwise None
    pub fn error(&self) -> Option<&E> {
        match self {
            FutureState::Error(error) => Some(error),
            _ => None,
        }
    }

    /// Maps the resolved value to a new type using the provided function
    pub fn map<U, F>(self, f: F) -> FutureState<U, E>
    where
        F: FnOnce(T) -> U,
    {
        match self {
            FutureState::Pending => FutureState::Pending,
            FutureState::Resolved(value) => FutureState::Resolved(f(value)),
            FutureState::Error(error) => FutureState::Error(error),
        }
    }

    /// Maps the error to a new type using the provided function
    pub fn map_err<F, G>(self, f: F) -> FutureState<T, G>
    where
        F: FnOnce(E) -> G,
    {
        match self {
            FutureState::Pending => FutureState::Pending,
            FutureState::Resolved(value) => FutureState::Resolved(value),
            FutureState::Error(error) => FutureState::Error(f(error)),
        }
    }
}

/// A handle to a future operation that provides access to its current state
///
/// This handle allows components to read the current state of an async operation
/// and provides utility methods for working with the future's result.
#[derive(Debug)]
pub struct FutureHandle<T, E = Box<dyn std::error::Error + Send + Sync>> {
    /// Reference to the shared future state container
    state: Arc<Mutex<FutureState<T, E>>>,
    /// Handle to the running task (for cancellation)
    task_handle: Arc<Mutex<Option<JoinHandle<()>>>>,
}

impl<T, E> FutureHandle<T, E>
where
    T: Clone,
    E: Clone,
{
    /// Create a new future handle with initial pending state
    fn new() -> Self {
        Self {
            state: Arc::new(Mutex::new(FutureState::Pending)),
            task_handle: Arc::new(Mutex::new(None)),
        }
    }

    /// Get the current state of the future
    pub fn state(&self) -> FutureState<T, E> {
        self.state.lock().clone()
    }

    /// Returns true if the future is currently pending
    pub fn is_pending(&self) -> bool {
        self.state().is_pending()
    }

    /// Returns true if the future has resolved successfully
    pub fn is_resolved(&self) -> bool {
        self.state().is_resolved()
    }

    /// Returns true if the future has failed with an error
    pub fn is_error(&self) -> bool {
        self.state().is_error()
    }

    /// Returns the resolved value if available, otherwise None
    pub fn value(&self) -> Option<T> {
        self.state().value().cloned()
    }

    /// Returns the error if available, otherwise None
    pub fn error(&self) -> Option<E> {
        self.state().error().cloned()
    }

    /// Cancel the running future if it's still pending
    pub fn cancel(&self) {
        if let Some(handle) = self.task_handle.lock().take() {
            handle.abort();
        }
    }

    /// Internal method to update the state
    fn set_state(&self, new_state: FutureState<T, E>) {
        *self.state.lock() = new_state;
    }

    /// Internal method to set the task handle
    fn set_task_handle(&self, handle: JoinHandle<()>) {
        *self.task_handle.lock() = Some(handle);
    }
}

impl<T, E> Clone for FutureHandle<T, E> {
    fn clone(&self) -> Self {
        Self {
            state: self.state.clone(),
            task_handle: self.task_handle.clone(),
        }
    }
}

/// Internal state for tracking future operations
struct FutureHookState<T, E> {
    /// Previous dependencies for comparison
    prev_deps: Option<Box<dyn EffectDependencies>>,
    /// Handle to the current future operation
    handle: FutureHandle<T, E>,
    /// Whether this hook has been initialized
    initialized: bool,
}

impl<T, E> FutureHookState<T, E>
where
    T: Clone,
    E: Clone,
{
    fn new() -> Self {
        Self {
            prev_deps: None,
            handle: FutureHandle::new(),
            initialized: false,
        }
    }
}

/// Error type for future operations that don't return a Result
#[derive(Debug, Clone)]
pub struct FutureError {
    message: String,
}

impl fmt::Display for FutureError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Future error: {}", self.message)
    }
}

impl std::error::Error for FutureError {}

/// Creates a new future handle for managing async operations in components/// React-style useFuture hook that provides async future management for components
///
/// This function provides future-based async operations with React-like behavior:
/// - Futures are executed when dependencies change or on first render
/// - Supports dependency arrays for conditional re-execution
/// - Provides comprehensive state management (pending, resolved, error)
/// - Handles future cancellation on dependency changes or component unmount
/// - Thread-safe and optimized for concurrent access
/// - Supports both Result-returning and direct value futures
///
/// # Examples
///
/// ## Basic Future (runs once on mount)
/// ```rust
/// use terminus_ui::prelude::*;
/// use tokio::time::{sleep, Duration};
///
/// #[component(DataFetcher)]
/// fn data_fetcher() -> Element {
///     // Fetch data once when component mounts
///     let future_handle = use_future(|| async {
///         sleep(Duration::from_secs(1)).await;
///         Ok::<String, Box<dyn std::error::Error + Send + Sync>>("Hello, World!".to_string())
///     }, Some(EmptyDeps));
///
///     match future_handle.state() {
///         FutureState::Pending => rsx! { <Text content="Loading..." /> },
///         FutureState::Resolved(data) => rsx! { <Text content={format!("Data: {}", data)} /> },
///         FutureState::Error(err) => rsx! { <Text content={format!("Error: {}", err)} /> },
///     }
/// }
/// ```
///
/// ## Future with Dependencies
/// ```rust
/// use terminus_ui::prelude::*;
/// use tokio::time::{sleep, Duration};
///
/// #[component(UserProfile)]
/// fn user_profile() -> Element {
///     let (user_id, _) = use_state(123);
///
///     // Re-fetch user data when user_id changes
///     let user_future = use_future(move || async move {
///         sleep(Duration::from_millis(500)).await;
///         fetch_user_data(user_id).await
///     }, Some(user_id));
///
///     match user_future.state() {
///         FutureState::Pending => rsx! { <Text content="Loading user..." /> },
///         FutureState::Resolved(user) => rsx! { <Text content={format!("User: {:?}", user)} /> },
///         FutureState::Error(err) => rsx! { <Text content={format!("Failed to load user: {}", err)} /> },
///     }
/// }
/// ```
///
/// ## Future with Manual Triggering
/// ```rust
/// use terminus_ui::prelude::*;
/// use tokio::time::{sleep, Duration};
///
/// #[component(ManualFetch)]
/// fn manual_fetch() -> Element {
///     let (trigger, set_trigger) = use_state(0);
///
///     // Re-run future when trigger changes
///     let data_future = use_future(move || async move {
///         sleep(Duration::from_secs(1)).await;
///         Ok::<String, Box<dyn std::error::Error + Send + Sync>>(
///             format!("Data fetched at trigger: {}", trigger)
///         )
///     }, Some(trigger));
///
///     rsx! {
///         <Layout direction={Direction::Vertical}>
///             <Button
///                 text="Fetch Data"
///                 on_click={move || set_trigger.update(|prev| prev + 1)}
///             />
///             {match data_future.state() {
///                 FutureState::Pending => rsx! { <Text content="Fetching..." /> },
///                 FutureState::Resolved(data) => rsx! { <Text content={data} /> },
///                 FutureState::Error(err) => rsx! { <Text content={format!("Error: {}", err)} /> },
///             }}
///         </Layout>
///     }
/// }
/// ```
///
/// # Error Handling
///
/// This function will panic if called outside of a component render context.
/// Always ensure useFuture is called within a component function.
///
/// # Performance Notes
///
/// - Futures are automatically cancelled when dependencies change
/// - State updates are thread-safe and optimized for concurrent access
/// - Dependency comparison uses PartialEq for efficient change detection
/// - Multiple futures in the same component are managed independently
/// - Memory usage is minimal with Arc-based sharing
pub fn use_future<Deps, F, Fut, T, E>(
    future_factory: F,
    deps: impl Into<Option<Deps>>,
) -> FutureHandle<T, E>
where
    Deps: EffectDependencies + Clone + PartialEq + 'static,
    F: FnOnce() -> Fut + Send + 'static,
    Fut: std::future::Future<Output = Result<T, E>> + Send + 'static,
    T: Clone + Send + 'static,
    E: Clone + Send + 'static,
{
    let deps = deps.into();
    with_hook_context(|ctx| {
        let hook_index = ctx.next_hook_index();
        let mut states = ctx.states.borrow_mut();

        // Get or create future state for this hook
        let future_state = states
            .entry(hook_index)
            .or_insert_with(|| Box::new(FutureHookState::<T, E>::new()))
            .downcast_mut::<FutureHookState<T, E>>()
            .expect("Future state type mismatch");

        // Determine if future should run
        let should_run = match &deps {
            None => {
                // No dependencies - run on every render (usually not recommended for futures)
                true
            }
            Some(current_deps) => {
                // Check if dependencies have changed
                match &future_state.prev_deps {
                    None => {
                        // First run - always execute
                        true
                    }
                    Some(prev_deps) => {
                        // Compare dependencies
                        !current_deps.deps_eq(prev_deps.as_ref())
                    }
                }
            }
        };

        if should_run {
            // Cancel any existing future
            future_state.handle.cancel();

            // Reset state to pending
            future_state.handle.set_state(FutureState::Pending);

            // Store new dependencies
            if let Some(current_deps) = &deps {
                future_state.prev_deps = Some(current_deps.clone_deps());
            } else {
                future_state.prev_deps = None;
            }

            // Create clones for the async task
            let handle_clone = future_state.handle.clone();

            // Spawn the future
            let task_handle = tokio::spawn(async move {
                let result = spawn_catch_panic(async move {
                    match future_factory().await {
                        Ok(value) => {
                            handle_clone.set_state(FutureState::Resolved(value));
                        }
                        Err(error) => {
                            handle_clone.set_state(FutureState::Error(error));
                        }
                    }
                })
                .await;

                // Handle any panic that occurred
                if result.is_err() {
                    // If the task panicked, we don't need to do anything special
                    // as the panic handler will have already logged it
                }
            });

            // Store the task handle for potential cancellation
            future_state.handle.set_task_handle(task_handle);
            future_state.initialized = true;
        }

        // Return a clone of the handle
        future_state.handle.clone()
    })
}
