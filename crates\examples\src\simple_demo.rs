use crossterm::event::{Event, KeyCode};
use rink::prelude::*;
use std::time::{Duration, Instant};

/// Simple interactive counter app using use_app and use_event with exit confirmation
fn build_ui() -> impl Into<VNode> {
    let (app, controller) = use_app();

    // Use local state for UI state and counter
    let (showing_exit_confirmation, set_showing_exit_confirmation) = use_state(false);
    let (exit_setup_done, set_exit_setup_done) = use_state(false);
    let (exit_time, set_exit_time) = use_state(None::<Instant>);
    let (counter, set_counter) = use_state(0i32);

    // Set up exit interception on first render
    if !exit_setup_done.get() {
        set_exit_setup_done.set(true);

        // Set up exit interceptor for confirmation
        let set_confirmation = set_showing_exit_confirmation.clone();
        controller.set_exit_interceptor(move |reason| {
            match reason {
                ExitReason::UserRequested => {
                    // Show confirmation dialog instead of exiting
                    set_confirmation.set(true);
                    false // Prevent exit
                }
                _ => true, // Allow other exit reasons to proceed
            }
        });
    }

    // Add an interval to update the countdown display every 100ms
    use_interval(
        {
            let controller = controller.clone();
            let app = app.clone();
            let exit_time = exit_time.clone();
            move || {
                if app.should_exit() {
                    if let Some(exit_time_instant) = exit_time.get() {
                        let elapsed = exit_time_instant.elapsed();
                        if elapsed >= Duration::from_secs(3) {
                            controller.shutdown();
                        }
                    }
                }
            }
        },
        Duration::from_millis(100),
    );

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        if showing_exit_confirmation.get() {
            // Handle exit confirmation dialog
            match key.code {
                KeyCode::Char('y') | KeyCode::Enter => {
                    // User confirmed exit - clear interceptor and exit
                    controller.clear_exit_interceptor();
                    set_exit_time.set(Some(Instant::now()));
                    controller.exit(ExitReason::UserRequested);
                }
                KeyCode::Char('n') | KeyCode::Esc => {
                    // User cancelled exit - hide confirmation dialog
                    set_showing_exit_confirmation.set(false);
                }
                _ => {}
            }
        } else {
            // Normal app controls
            match key.code {
                KeyCode::Char('q') => {
                    // Try to exit - will be intercepted and show confirmation
                    controller.exit(ExitReason::UserRequested);
                }
                KeyCode::Up | KeyCode::Char('+') | KeyCode::Char('=') => {
                    set_counter.update(|current| current + 1);
                }
                KeyCode::Down | KeyCode::Char('-') => {
                    set_counter.update(|current| current - 1);
                }
                KeyCode::Char('r') | KeyCode::Char('0') => {
                    set_counter.set(0);
                }
                KeyCode::Esc => {
                    // Alternative exit key - also gets intercepted
                    controller.exit(ExitReason::UserRequested);
                }
                _ => {}
            }
        }
    }

    // Handle app exit with countdown
    if app.should_exit() {
        if let Some(exit_time_instant) = exit_time.get() {
            let elapsed = exit_time_instant.elapsed();
            let remaining = 3.0 - elapsed.as_secs_f32();

            // Check if we're in final shutdown phase
            if app.lifecycle() == AppLifecycle::Shutdown {
                // Render final frame before exiting
                return rsx! {
                    <Block
                        title="Shutting Down..."
                        borders={Borders::ALL}
                        border_style={Style::default().fg(Color::Red)}
                    >
                        <Paragraph
                            style={Style::default().fg(Color::White)}
                            alignment={Alignment::Center}
                        >
                            {"Application closed.\n\nThis demonstrates the should_exit() functionality!"}
                        </Paragraph>
                    </Block>
                };
            }

            return rsx! {
                <Block
                    title="Goodbye!"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Yellow)}
                >
                    <Paragraph
                        style={Style::default().fg(Color::White)}
                        alignment={Alignment::Center}
                    >
                        {format!(
                            "🚀 Thanks for using Rink-Leptos TUI! 🚀\n\n\
                            Exit requested by user\n\n\
                            Closing in {:.1} seconds...\n\n\
                            (This demonstrates the should_exit() functionality)\n\
                            Watch the countdown update smoothly!",
                            remaining.max(0.0)
                        )}
                    </Paragraph>
                </Block>
            };
        }
    }

    // Show exit confirmation dialog if needed
    if showing_exit_confirmation.get() {
        return rsx! {
            <Block
                title="Exit Confirmation"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Paragraph
                    style={Style::default().fg(Color::White)}
                    alignment={Alignment::Center}
                >
                    {"🤔 Are you sure you want to quit?\n\n\
                    This demonstrates the exit interception feature!\n\n\
                    Press 'y' or Enter to confirm exit\n\
                    Press 'n' or Esc to cancel\n\n\
                    Exit interception allows apps to:\n\
                    • Show confirmation dialogs\n\
                    • Save unsaved work\n\
                    • Prevent accidental exits"}
                </Paragraph>
            </Block>
        };
    }

    // Get current counter value from use_state
    let counter_value = counter.get();
    let metrics = app.metrics();

    // Choose border color based on counter value
    let border_color = match counter_value {
        n if n > 0 => Color::Green,
        n if n < 0 => Color::Red,
        _ => Color::Cyan,
    };

    rsx! {
        <Block
            title="Rink-Leptos Interactive Demo"
            borders={Borders::ALL}
            border_style={Style::default().fg(border_color)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!(
                    "🚀 Welcome to Rink-Leptos TUI Framework! 🚀\n\n\
                    Counter: {}\n\n\
                    Controls:\n\
                    ↑ / + : Increment\n\
                    ↓ / - : Decrement\n\
                    r / 0 : Reset\n\
                    q / Esc : Quit (with confirmation)\n\n\
                    Features demonstrated:\n\
                    • use_app hook for global state\n\
                    • use_state hook for local UI state\n\
                    • Exit interception & confirmation\n\
                    • Event handling with use_event\n\n\
                    Renders: {} | Events: {}",
                    counter_value,
                    metrics.render_count,
                    metrics.event_count
                )}
            </Paragraph>
        </Block>
    }
}

/// Entry point of the application
fn main() -> Result<(), Box<dyn std::error::Error>> {
    render(build_ui)
}
