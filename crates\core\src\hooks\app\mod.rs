//! Professional use_app hook for application-level state management
//!
//! This module provides a comprehensive application state management hook inspired by ink's
//! approach, offering centralized state management, lifecycle control, and event handling
//! for TUI applications.

use crossterm::event::Event;
use dashmap::DashMap;
use parking_lot::{Mutex, RwLock};
use std::sync::{
    Arc,
    atomic::{AtomicBool, Ordering},
};
use std::time::{Duration, Instant};

#[cfg(test)]
mod tests;

// Global exit flag for the application
static GLOBAL_EXIT_FLAG: AtomicBool = AtomicBool::new(false);

// Global app container shared across all use_app() calls
static GLOBAL_APP_CONTAINER: Mutex<Option<Arc<AppContainer>>> = Mutex::new(None);

/// Check if the application should exit globally
pub fn should_exit_globally() -> bool {
    GLOBAL_EXIT_FLAG.load(Ordering::Relaxed)
}

/// Set the global exit flag
pub fn set_global_exit_flag(should_exit: bool) {
    GLOBAL_EXIT_FLAG.store(should_exit, Ordering::Relaxed);
}

/// Reset the global app container (for testing)
#[cfg(test)]
pub fn reset_global_app_container() {
    *GLOBAL_APP_CONTAINER.lock() = None;
    set_global_exit_flag(false);
}

/// Application lifecycle states
#[derive(Debug, Clone, PartialEq)]
pub enum AppLifecycle {
    /// Application is initializing
    Initializing,
    /// Application is running normally
    Running,
    /// Application is pausing (e.g., background mode)
    Pausing,
    /// Application is paused
    Paused,
    /// Application is resuming from pause
    Resuming,
    /// Application is shutting down gracefully
    Shutting,
    /// Application has shut down
    Shutdown,
}

/// Application exit reasons
#[derive(Debug, Clone, PartialEq)]
pub enum ExitReason {
    /// User requested exit (e.g., Ctrl+C, 'q' key)
    UserRequested,
    /// Application completed successfully
    Completed,
    /// Application encountered an error
    Error(String),
    /// System requested shutdown
    SystemShutdown,
    /// Timeout occurred
    Timeout,
}

/// Exit interception callback type
///
/// This callback is called when an exit is requested and should return:
/// - `true` to allow the exit to proceed
/// - `false` to prevent the exit and keep the application running
pub type ExitInterceptor = Box<dyn Fn(ExitReason) -> bool + Send + Sync>;

/// Application configuration and settings
#[derive(Debug, Clone)]
pub struct AppConfig {
    /// Application name
    pub name: String,
    /// Application version
    pub version: String,
    /// Enable debug mode
    pub debug: bool,
    /// Auto-save interval
    pub auto_save_interval: Option<Duration>,
    /// Maximum event queue size
    pub max_event_queue_size: usize,
    /// Enable performance monitoring
    pub enable_performance_monitoring: bool,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            name: "RinkApp".to_string(),
            version: "1.0.0".to_string(),
            debug: false,
            auto_save_interval: None,
            max_event_queue_size: 1000,
            enable_performance_monitoring: false,
        }
    }
}

/// Application performance metrics
#[derive(Debug, Clone, Default)]
pub struct AppMetrics {
    /// Application start time
    pub start_time: Option<Instant>,
    /// Total render count
    pub render_count: u64,
    /// Total event count
    pub event_count: u64,
    /// Average frame time in milliseconds
    pub avg_frame_time_ms: f64,
    /// Memory usage in bytes (if available)
    pub memory_usage_bytes: Option<u64>,
    /// Last update time
    pub last_update: Option<Instant>,
}

/// Application state container
pub struct AppState {
    /// Current lifecycle state
    pub lifecycle: AppLifecycle,
    /// Application configuration
    pub config: AppConfig,
    /// Exit reason (if any)
    pub exit_reason: Option<ExitReason>,
    /// Whether the application should exit
    pub should_exit: bool,
    /// Application-wide data store
    pub data: Arc<DashMap<String, Box<dyn std::any::Any + Send + Sync>>>,
    /// Performance metrics
    pub metrics: AppMetrics,
    /// Last processed event
    pub last_event: Option<Event>,
    /// Event processing enabled
    pub event_processing_enabled: bool,
    /// Exit interception callback
    pub exit_interceptor: Option<Arc<ExitInterceptor>>,
}

impl AppState {
    /// Create a new application state with default configuration
    pub fn new() -> Self {
        Self::with_config(AppConfig::default())
    }

    /// Create a new application state with custom configuration
    pub fn with_config(config: AppConfig) -> Self {
        Self {
            lifecycle: AppLifecycle::Initializing,
            config,
            exit_reason: None,
            should_exit: false,
            data: Arc::new(DashMap::new()),
            metrics: AppMetrics {
                start_time: Some(Instant::now()),
                ..Default::default()
            },
            last_event: None,
            event_processing_enabled: true,
            exit_interceptor: None,
        }
    }

    /// Set a value in the application data store
    pub fn set_data<T: Send + Sync + 'static>(&self, key: &str, value: T) {
        self.data.insert(key.to_string(), Box::new(value));
    }

    /// Get a value from the application data store
    pub fn get_data<T: Send + Sync + Clone + 'static>(&self, key: &str) -> Option<T> {
        self.data.get(key)?.downcast_ref::<T>().cloned()
    }

    /// Remove a value from the application data store
    pub fn remove_data(&self, key: &str) -> Option<Box<dyn std::any::Any + Send + Sync>> {
        self.data.remove(key).map(|(_, value)| value)
    }

    /// Update performance metrics
    pub fn update_metrics(&mut self) {
        self.metrics.render_count += 1;
        self.metrics.last_update = Some(Instant::now());

        if let Some(start_time) = self.metrics.start_time {
            let elapsed = start_time.elapsed();
            self.metrics.avg_frame_time_ms =
                elapsed.as_millis() as f64 / self.metrics.render_count as f64;
        }
    }

    /// Record an event
    pub fn record_event(&mut self, event: Event) {
        self.metrics.event_count += 1;
        self.last_event = Some(event);
    }
}

impl Default for AppState {
    fn default() -> Self {
        Self::new()
    }
}

impl std::fmt::Debug for AppState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("AppState")
            .field("lifecycle", &self.lifecycle)
            .field("config", &self.config)
            .field("exit_reason", &self.exit_reason)
            .field("should_exit", &self.should_exit)
            .field("data", &format!("DashMap with {} items", self.data.len()))
            .field("metrics", &self.metrics)
            .field("last_event", &self.last_event)
            .field("event_processing_enabled", &self.event_processing_enabled)
            .field(
                "exit_interceptor",
                &self
                    .exit_interceptor
                    .as_ref()
                    .map(|_| "Some(ExitInterceptor)"),
            )
            .finish()
    }
}

impl Clone for AppState {
    fn clone(&self) -> Self {
        Self {
            lifecycle: self.lifecycle.clone(),
            config: self.config.clone(),
            exit_reason: self.exit_reason.clone(),
            should_exit: self.should_exit,
            data: self.data.clone(), // DashMap can be cloned (Arc<DashMap>)
            metrics: self.metrics.clone(),
            last_event: self.last_event.clone(),
            event_processing_enabled: self.event_processing_enabled,
            exit_interceptor: self.exit_interceptor.clone(),
        }
    }
}

/// Application state container with thread-safe access
#[derive(Clone)]
pub struct AppContainer {
    state: Arc<RwLock<AppState>>,
    version: Arc<Mutex<u64>>,
}

impl AppContainer {
    /// Create a new application container
    pub fn new(initial_state: AppState) -> Self {
        Self {
            state: Arc::new(RwLock::new(initial_state)),
            version: Arc::new(Mutex::new(0)),
        }
    }

    /// Get the current application state
    pub fn get_state(&self) -> AppState {
        self.state.read().clone()
    }

    /// Update the application state
    pub fn update_state<F>(&self, updater: F)
    where
        F: FnOnce(&mut AppState),
    {
        {
            let mut state = self.state.write();
            updater(&mut state);
            state.update_metrics();
        }

        // Increment version counter
        {
            let mut version = self.version.lock();
            *version += 1;
        }
    }

    /// Get the current version (for change detection)
    pub fn get_version(&self) -> u64 {
        *self.version.lock()
    }
}

/// Handle for reading application state
#[derive(Clone)]
pub struct AppHandle {
    container: Arc<AppContainer>,
    last_version: Arc<Mutex<u64>>,
}

impl AppHandle {
    /// Create a new app handle from a container
    fn from_container(container: Arc<AppContainer>) -> Self {
        Self {
            container,
            last_version: Arc::new(Mutex::new(0)), // Start at 0 to detect initial changes
        }
    }

    /// Get the current application state
    pub fn get(&self) -> AppState {
        let state = self.container.get_state();
        *self.last_version.lock() = self.container.get_version();
        state
    }

    /// Get a specific field from the application state efficiently
    pub fn field<T, F>(&self, accessor: F) -> T
    where
        F: FnOnce(&AppState) -> T,
    {
        let state = self.container.state.read();
        *self.last_version.lock() = self.container.get_version();
        accessor(&state)
    }

    /// Check if the application state has changed since last access
    pub fn has_changed(&self) -> bool {
        let current_version = self.container.get_version();
        let last_version = *self.last_version.lock();
        current_version != last_version
    }

    /// Get the current version of the application state
    pub fn version(&self) -> u64 {
        self.container.get_version()
    }

    /// Get the current lifecycle state
    pub fn lifecycle(&self) -> AppLifecycle {
        self.field(|state| state.lifecycle.clone())
    }

    /// Check if the application should exit
    pub fn should_exit(&self) -> bool {
        self.field(|state| state.should_exit)
    }

    /// Get the exit reason if any
    pub fn exit_reason(&self) -> Option<ExitReason> {
        self.field(|state| state.exit_reason.clone())
    }

    /// Get application metrics
    pub fn metrics(&self) -> AppMetrics {
        self.field(|state| state.metrics.clone())
    }

    /// Get application configuration
    pub fn config(&self) -> AppConfig {
        self.field(|state| state.config.clone())
    }
}

/// Controller for managing application state and lifecycle
#[derive(Clone)]
pub struct AppController {
    container: Arc<AppContainer>,
}

impl AppController {
    /// Create a new app controller from a container
    fn from_container(container: Arc<AppContainer>) -> Self {
        Self { container }
    }

    /// Initialize the application
    pub fn initialize(&self) {
        self.container.update_state(|state| {
            state.lifecycle = AppLifecycle::Running;
        });
    }

    /// Request application exit with a reason
    ///
    /// This method will first check if there's an exit interceptor registered.
    /// If there is, it will call the interceptor with the exit reason.
    /// If the interceptor returns false, the exit will be cancelled.
    /// If the interceptor returns true or no interceptor is registered, the exit will proceed.
    pub fn exit(&self, reason: ExitReason) {
        // Check if there's an exit interceptor
        let should_proceed = {
            let state = self.container.state.read();
            if let Some(interceptor) = &state.exit_interceptor {
                // Call the interceptor with the exit reason
                interceptor(reason.clone())
            } else {
                // No interceptor, proceed with exit
                true
            }
        };

        // Only proceed with exit if interceptor allows it
        if should_proceed {
            self.container.update_state(|state| {
                state.should_exit = true;
                state.exit_reason = Some(reason);
                state.lifecycle = AppLifecycle::Shutting;
            });
            // Note: We don't set the global exit flag here. The application should call
            // shutdown() when it's ready to actually exit (e.g., after showing a countdown).
            // This allows the app to control the exit timing and show shutdown UI.
        }
        // If should_proceed is false, the exit is cancelled and the app continues running
    }

    /// Register an exit interceptor callback
    ///
    /// The interceptor will be called whenever an exit is requested.
    /// It should return true to allow the exit or false to prevent it.
    ///
    /// # Example
    /// ```rust,no_run
    /// # use rink_core::hooks::app::{use_app, ExitReason};
    /// let (app, controller) = use_app();
    ///
    /// controller.set_exit_interceptor(|reason| {
    ///     match reason {
    ///         ExitReason::UserRequested => {
    ///             // Show confirmation dialog
    ///             // Return false to prevent exit, true to allow
    ///             false
    ///         }
    ///         _ => true, // Allow other exit reasons
    ///     }
    /// });
    /// ```
    pub fn set_exit_interceptor<F>(&self, interceptor: F)
    where
        F: Fn(ExitReason) -> bool + Send + Sync + 'static,
    {
        self.container.update_state(|state| {
            state.exit_interceptor = Some(Arc::new(Box::new(interceptor)));
        });
    }

    /// Remove the exit interceptor
    pub fn clear_exit_interceptor(&self) {
        self.container.update_state(|state| {
            state.exit_interceptor = None;
        });
    }

    /// Pause the application
    pub fn pause(&self) {
        self.container.update_state(|state| {
            if state.lifecycle == AppLifecycle::Running {
                state.lifecycle = AppLifecycle::Pausing;
            }
        });
    }

    /// Resume the application
    pub fn resume(&self) {
        self.container.update_state(|state| {
            if matches!(
                state.lifecycle,
                AppLifecycle::Paused | AppLifecycle::Pausing
            ) {
                state.lifecycle = AppLifecycle::Resuming;
            }
        });
    }

    /// Complete pause transition
    pub fn complete_pause(&self) {
        self.container.update_state(|state| {
            if state.lifecycle == AppLifecycle::Pausing {
                state.lifecycle = AppLifecycle::Paused;
            }
        });
    }

    /// Complete resume transition
    pub fn complete_resume(&self) {
        self.container.update_state(|state| {
            if state.lifecycle == AppLifecycle::Resuming {
                state.lifecycle = AppLifecycle::Running;
            }
        });
    }

    /// Update application configuration
    pub fn update_config<F>(&self, updater: F)
    where
        F: FnOnce(&mut AppConfig),
    {
        self.container.update_state(|state| {
            updater(&mut state.config);
        });
    }

    /// Set application data
    pub fn set_data<T: Send + Sync + 'static>(&self, key: &str, value: T) {
        // Access DashMap directly for better performance
        let state = self.container.state.read();
        state.set_data(key, value);
        drop(state);

        // Increment version counter manually for change detection
        {
            let mut version = self.container.version.lock();
            *version += 1;
        }
    }

    /// Remove application data
    pub fn remove_data(&self, key: &str) {
        // Access DashMap directly for better performance
        let state = self.container.state.read();
        state.remove_data(key);
        drop(state);

        // Increment version counter manually for change detection
        {
            let mut version = self.container.version.lock();
            *version += 1;
        }
    }

    /// Record an event
    pub fn record_event(&self, event: Event) {
        self.container.update_state(|state| {
            state.record_event(event);
        });
    }

    /// Enable or disable event processing
    pub fn set_event_processing(&self, enabled: bool) {
        self.container.update_state(|state| {
            state.event_processing_enabled = enabled;
        });
    }

    /// Shutdown the application gracefully
    pub fn shutdown(&self) {
        self.container.update_state(|state| {
            state.lifecycle = AppLifecycle::Shutdown;
            state.should_exit = true;
            if state.exit_reason.is_none() {
                state.exit_reason = Some(ExitReason::Completed);
            }
        });
        // Set the global exit flag so the renderer knows to stop
        set_global_exit_flag(true);
    }
}

/// Professional use_app hook for application-level state management
///
/// This hook provides comprehensive application state management inspired by ink's approach,
/// offering centralized state management, lifecycle control, and event handling for TUI applications.
///
/// # Features
///
/// - **Lifecycle Management**: Track application states (initializing, running, paused, shutting down)
/// - **Exit Handling**: Graceful shutdown with exit reasons
/// - **Global Data Store**: Application-wide key-value storage with type safety
/// - **Performance Metrics**: Built-in performance monitoring and metrics collection
/// - **Event Integration**: Seamless integration with the event system
/// - **Configuration Management**: Dynamic configuration updates
/// - **Thread Safety**: Full thread-safe access for async applications
///
/// # Examples
///
/// ## Basic Application Setup
/// ```rust,no_run
/// use rink_core::hooks::app::{use_app, AppConfig, ExitReason};
/// use rink::prelude::*;
///
/// fn my_app() -> impl Renderable {
///     let (app, controller) = use_app();
///
///     // Check if app should exit
///     if app.should_exit() {
///         // Handle graceful shutdown
///         return rsx! {
///             <Block title="Shutting Down">
///                 <Paragraph>{"Application is shutting down..."}</Paragraph>
///             </Block>
///         };
///     }
///
///     // Handle events
///     if let Some(Event::Key(key)) = use_event() {
///         match key.code {
///             KeyCode::Char('q') => {
///                 controller.exit(ExitReason::UserRequested);
///             }
///             _ => {}
///         }
///     }
///
///     rsx! {
///         <Block title="My App">
///             <Paragraph>{format!("Lifecycle: {:?}", app.lifecycle())}</Paragraph>
///         </Block>
///     }
/// }
/// ```
///
/// ## Application with Custom Configuration
/// ```rust,no_run
/// use rink_core::hooks::app::{use_app_with_config, AppConfig};
/// use std::time::Duration;
///
/// fn configured_app() -> impl Renderable {
///     let config = AppConfig {
///         name: "MyTUIApp".to_string(),
///         version: "2.0.0".to_string(),
///         debug: true,
///         auto_save_interval: Some(Duration::from_secs(30)),
///         ..Default::default()
///     };
///
///     let (app, controller) = use_app_with_config(config);
///
///     // App implementation...
///     rsx! {
///         <Block title={app.config().name}>
///             <Paragraph>{format!("Version: {}", app.config().version)}</Paragraph>
///         </Block>
///     }
/// }
/// ```
///
/// ## Global Data Storage
/// ```rust,no_run
/// use rink_core::hooks::app::use_app;
///
/// #[derive(Clone)]
/// struct UserSession {
///     username: String,
///     login_time: std::time::Instant,
/// }
///
/// fn app_with_data() -> impl Renderable {
///     let (app, controller) = use_app();
///
///     // Store user session data
///     let session = UserSession {
///         username: "john_doe".to_string(),
///         login_time: std::time::Instant::now(),
///     };
///     controller.set_data("user_session", session);
///
///     // Access stored data
///     let has_session = app.field(|state| {
///         state.get_data::<UserSession>("user_session").is_some()
///     });
///
///     rsx! {
///         <Block title="User Status">
///             <Paragraph>{
///                 if has_session {
///                     "User logged in"
///                 } else {
///                     "No active session"
///                 }
///             }</Paragraph>
///         </Block>
///     }
/// }
/// ```
///
/// ## Performance Monitoring
/// ```rust,no_run
/// use rink_core::hooks::app::use_app;
///
/// fn performance_app() -> impl Renderable {
///     let (app, _controller) = use_app();
///     let metrics = app.metrics();
///
///     rsx! {
///         <Block title="Performance">
///             <Paragraph>{format!(
///                 "Renders: {}\nEvents: {}\nAvg Frame Time: {:.2}ms",
///                 metrics.render_count,
///                 metrics.event_count,
///                 metrics.avg_frame_time_ms
///             )}</Paragraph>
///         </Block>
///     }
/// }
/// ```
///
/// # Thread Safety
///
/// Both the AppHandle and AppController are thread-safe and can be safely shared
/// across async tasks and threads.
///
/// # Performance Notes
///
/// - State reads are optimized using RwLock for concurrent access
/// - Metrics are automatically updated on each render
/// - Change detection prevents unnecessary re-renders
/// - Memory usage is tracked when available
pub fn use_app() -> (AppHandle, AppController) {
    use_app_with_config(AppConfig::default())
}

/// Create an application hook with custom configuration
///
/// This is the same as `use_app()` but allows you to specify custom application
/// configuration including name, version, debug settings, and performance options.
///
/// # Arguments
///
/// * `config` - Application configuration settings
///
/// # Returns
///
/// A tuple containing:
/// - `AppHandle` - Read-only access to application state
/// - `AppController` - Methods for controlling application lifecycle and state
pub fn use_app_with_config(config: AppConfig) -> (AppHandle, AppController) {
    // Get or initialize the global app container
    let container = {
        let mut global_container = GLOBAL_APP_CONTAINER.lock();
        if global_container.is_none() {
            *global_container = Some(Arc::new(AppContainer::new(AppState::with_config(config))));
        }
        global_container.as_ref().unwrap().clone()
    };

    // Create the app handle
    let app_handle = AppHandle::from_container(container.clone());

    // Create the controller
    let controller = AppController::from_container(container.clone());

    // Initialize the app if it's in the initializing state
    if app_handle.lifecycle() == AppLifecycle::Initializing {
        controller.initialize();
        // Update the app handle's last_version to match the current version after initialization
        *app_handle.last_version.lock() = container.get_version();
    }

    (app_handle, controller)
}
