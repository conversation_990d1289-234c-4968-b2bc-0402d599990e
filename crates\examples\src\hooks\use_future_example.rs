//! # Future Hooks Example
//!
//! This example demonstrates the powerful async capabilities of the rink-leptos framework
//! through comprehensive use of the `use_future` hook with progress tracking, error handling,
//! and real-world scenarios.
//!
//! ## Features Demonstrated:
//! - Basic async operations with `use_future`
//! - Progress tracking for long-running operations
//! - Error handling and recovery patterns
//! - Dependency-based re-execution
//! - Concurrent future management
//! - Real-world API simulation scenarios

use rink_core::hooks::future::{use_future, FutureOptions, FutureState};
use rink_core::hooks::state::use_state;
use rink_core::VNode;
use std::time::Duration;
use tokio::time::sleep;

/// Main example component showcasing various future hook patterns
pub fn future_hooks_showcase() -> VNode {
    VNode::Container(vec![
        create_header(),
        basic_future_example(),
        progress_tracking_example(),
        error_handling_example(),
        dependency_driven_example(),
        concurrent_operations_example(),
        real_world_api_example(),
    ])
}

/// Creates a beautiful header for the showcase
fn create_header() -> VNode {
    VNode::Text(format!(
        "{}",
        "
╔══════════════════════════════════════════════════════════════════════════════╗
║                          🚀 FUTURE HOOKS SHOWCASE 🚀                        ║
║                                                                              ║
║  Demonstrating async operations, progress tracking, and error handling       ║
║  in the rink-leptos TUI framework with professional patterns                 ║
╚══════════════════════════════════════════════════════════════════════════════╝
        "
        .trim()
    ))
}

/// Example 1: Basic Future Operations
/// Demonstrates simple async operations with clean state management
fn basic_future_example() -> VNode {
    let (refresh_trigger, set_refresh_trigger) = use_state(0);

    // Basic future that simulates data fetching
    let data_future = use_future(
        |_progress| async move {
            // Simulate network delay
            sleep(Duration::from_millis(800)).await;
            
            // Simulate data processing
            let data = vec![
                "🌟 User Dashboard",
                "📊 Analytics Panel", 
                "⚙️ Settings Menu",
                "📝 Recent Activity",
                "🔔 Notifications"
            ];
            
            Ok::<Vec<&str>, String>(data)
        },
        refresh_trigger,
    );

    let content = match data_future.state() {
        FutureState::Pending => "⏳ Loading dashboard components...".to_string(),
        FutureState::Progress(_) => "📡 Processing data...".to_string(),
        FutureState::Resolved(items) => {
            format!(
                "✅ Dashboard Ready!\n{}",
                items.iter()
                    .enumerate()
                    .map(|(i, item)| format!("  {}. {}", i + 1, item))
                    .collect::<Vec<_>>()
                    .join("\n")
            )
        }
        FutureState::Error(err) => format!("❌ Error: {}", err),
    };

    VNode::Container(vec![
        VNode::Text("┌─ 📋 Basic Future Operations ─────────────────────────────────────┐".to_string()),
        VNode::Text(format!("│ {}│", pad_content(&content, 66))),
        VNode::Text("│                                                                  │".to_string()),
        VNode::Text("│ 🔄 Press 'r' to refresh                                          │".to_string()),
        VNode::Text("└──────────────────────────────────────────────────────────────────┘".to_string()),
    ])
}

/// Example 2: Progress Tracking
/// Shows real-time progress updates for long-running operations
fn progress_tracking_example() -> VNode {
    let (start_download, set_start_download) = use_state(0);

    // Future with progress tracking for file download simulation
    let download_future = use_future(
        |progress_callback| async move {
            let total_chunks = 20;
            let mut downloaded = 0;

            for chunk in 1..=total_chunks {
                // Simulate downloading a chunk
                sleep(Duration::from_millis(100)).await;
                downloaded += chunk * 1024; // Simulate varying chunk sizes

                // Report progress
                if let Some(progress) = &progress_callback {
                    progress(chunk as f32 / total_chunks as f32);
                }
            }

            Ok::<String, String>(format!("Downloaded {} KB successfully!", downloaded))
        },
        start_download,
    ).with_progress(|progress| {
        // Custom progress handler - could update UI, log, etc.
        println!("Download progress: {:.1}%", progress * 100.0);
    });

    let content = match download_future.state() {
        FutureState::Pending => "📁 Ready to download...".to_string(),
        FutureState::Progress(progress) => {
            let percentage = (progress * 100.0) as u32;
            let bar_length = 30;
            let filled = ((progress * bar_length as f32) as usize).min(bar_length);
            let empty = bar_length - filled;
            
            format!(
                "📥 Downloading... {}%\n[{}{}]",
                percentage,
                "█".repeat(filled),
                "░".repeat(empty)
            )
        }
        FutureState::Resolved(result) => format!("✅ {}", result),
        FutureState::Error(err) => format!("❌ Download failed: {}", err),
    };

    VNode::Container(vec![
        VNode::Text("┌─ 📊 Progress Tracking ───────────────────────────────────────────┐".to_string()),
        VNode::Text(format!("│ {}│", pad_content(&content, 66))),
        VNode::Text("│                                                                  │".to_string()),
        VNode::Text("│ 📥 Press 'd' to start download                                   │".to_string()),
        VNode::Text("└──────────────────────────────────────────────────────────────────┘".to_string()),
    ])
}

/// Example 3: Error Handling and Recovery
/// Demonstrates robust error handling patterns with retry mechanisms
fn error_handling_example() -> VNode {
    let (attempt_count, set_attempt_count) = use_state(0);
    let (force_error, set_force_error) = use_state(false);

    let api_call_future = use_future(
        |_progress| async move {
            // Simulate API call with potential failures
            sleep(Duration::from_millis(500)).await;
            
            // Simulate random failures for demonstration
            if force_error && attempt_count % 3 != 0 {
                return Err("🌐 Network timeout - please try again".to_string());
            }

            // Simulate successful API response
            Ok(format!(
                "🎉 API Success! Attempt #{}\n📊 Retrieved 42 records\n⏱️ Response time: 245ms",
                attempt_count + 1
            ))
        },
        (attempt_count, force_error),
    );

    let content = match api_call_future.state() {
        FutureState::Pending => "🔄 Calling API...".to_string(),
        FutureState::Progress(_) => "📡 Processing request...".to_string(),
        FutureState::Resolved(result) => result,
        FutureState::Error(err) => {
            format!("{}\n🔄 Retry available (attempt #{})", err, attempt_count + 1)
        }
    };

    VNode::Container(vec![
        VNode::Text("┌─ 🛡️ Error Handling & Recovery ───────────────────────────────────┐".to_string()),
        VNode::Text(format!("│ {}│", pad_content(&content, 66))),
        VNode::Text("│                                                                  │".to_string()),
        VNode::Text("│ 🔄 Press 'a' to call API  │  ❌ Press 'e' to toggle errors     │".to_string()),
        VNode::Text("└──────────────────────────────────────────────────────────────────┘".to_string()),
    ])
}

/// Example 4: Dependency-Driven Re-execution
/// Shows how futures automatically re-run when dependencies change
fn dependency_driven_example() -> VNode {
    let (user_id, set_user_id) = use_state(1);
    let (include_details, set_include_details) = use_state(false);

    // Future that depends on user_id and include_details
    let user_profile_future = use_future(
        |_progress| async move {
            sleep(Duration::from_millis(300)).await;
            
            let base_profile = format!("👤 User #{}\n📧 user{}@example.com", user_id, user_id);
            
            if include_details {
                Ok(format!(
                    "{}\n🏢 Software Engineer\n📍 San Francisco, CA\n⭐ Premium Member",
                    base_profile
                ))
            } else {
                Ok(base_profile)
            }
        },
        (user_id, include_details),
    );

    let content = match user_profile_future.state() {
        FutureState::Pending => "👤 Loading user profile...".to_string(),
        FutureState::Progress(_) => "🔍 Fetching user data...".to_string(),
        FutureState::Resolved(profile) => profile,
        FutureState::Error(err) => format!("❌ Failed to load profile: {}", err),
    };

    VNode::Container(vec![
        VNode::Text("┌─ 🔄 Dependency-Driven Updates ───────────────────────────────────┐".to_string()),
        VNode::Text(format!("│ {}│", pad_content(&content, 66))),
        VNode::Text("│                                                                  │".to_string()),
        VNode::Text(format!("│ Current: User {} | Details: {}                              │", 
            user_id, if include_details { "ON " } else { "OFF" })),
        VNode::Text("│ ⬆️ Press 'u' for next user  │  ℹ️ Press 'i' to toggle details   │".to_string()),
        VNode::Text("└──────────────────────────────────────────────────────────────────┘".to_string()),
    ])
}

/// Example 5: Concurrent Operations
/// Demonstrates managing multiple concurrent futures
fn concurrent_operations_example() -> VNode {
    let (start_batch, set_start_batch) = use_state(0);

    // Multiple concurrent operations
    let weather_future = use_future(
        |_progress| async move {
            sleep(Duration::from_millis(400)).await;
            Ok("🌤️ 72°F, Partly Cloudy".to_string())
        },
        start_batch,
    );

    let news_future = use_future(
        |_progress| async move {
            sleep(Duration::from_millis(600)).await;
            Ok("📰 Tech stocks rise 2.3%".to_string())
        },
        start_batch,
    );

    let notifications_future = use_future(
        |_progress| async move {
            sleep(Duration::from_millis(200)).await;
            Ok("🔔 3 new messages".to_string())
        },
        start_batch,
    );

    let weather_status = format_future_status("Weather", &weather_future);
    let news_status = format_future_status("News", &news_future);
    let notifications_status = format_future_status("Notifications", &notifications_future);

    VNode::Container(vec![
        VNode::Text("┌─ 🔀 Concurrent Operations ───────────────────────────────────────┐".to_string()),
        VNode::Text(format!("│ {}│", pad_content(&weather_status, 66))),
        VNode::Text(format!("│ {}│", pad_content(&news_status, 66))),
        VNode::Text(format!("│ {}│", pad_content(&notifications_status, 66))),
        VNode::Text("│                                                                  │".to_string()),
        VNode::Text("│ 🚀 Press 'b' to start batch operations                          │".to_string()),
        VNode::Text("└──────────────────────────────────────────────────────────────────┘".to_string()),
    ])
}

/// Example 6: Real-World API Simulation
/// Complex example simulating a real application workflow
fn real_world_api_example() -> VNode {
    let (workflow_step, set_workflow_step) = use_state(0);

    let workflow_future = use_future(
        |progress_callback| async move {
            let steps = vec![
                ("🔐 Authenticating user", 200),
                ("📊 Fetching dashboard data", 400),
                ("🎨 Rendering components", 300),
                ("✨ Applying user preferences", 250),
                ("🚀 Finalizing setup", 150),
            ];

            let mut results = Vec::new();
            
            for (i, (step_name, duration)) in steps.iter().enumerate() {
                if let Some(progress) = &progress_callback {
                    progress(i as f32 / steps.len() as f32);
                }
                
                sleep(Duration::from_millis(*duration)).await;
                results.push(format!("✅ {}", step_name));
            }

            if let Some(progress) = &progress_callback {
                progress(1.0);
            }

            Ok(format!(
                "🎉 Application Ready!\n\n{}\n\n💫 Welcome to your personalized dashboard!",
                results.join("\n")
            ))
        },
        workflow_step,
    ).with_progress(|progress| {
        // Real-world progress tracking could update a progress bar component
        if progress >= 1.0 {
            println!("🎊 Workflow completed successfully!");
        }
    });

    let content = match workflow_future.state() {
        FutureState::Pending => "⏳ Initializing application...".to_string(),
        FutureState::Progress(progress) => {
            let percentage = (progress * 100.0) as u32;
            format!("🔄 Setting up your experience... {}%", percentage)
        }
        FutureState::Resolved(result) => result,
        FutureState::Error(err) => format!("💥 Setup failed: {}", err),
    };

    VNode::Container(vec![
        VNode::Text("┌─ 🌟 Real-World Application Workflow ─────────────────────────────┐".to_string()),
        VNode::Text(format!("│ {}│", pad_content(&content, 66))),
        VNode::Text("│                                                                  │".to_string()),
        VNode::Text("│ 🌟 Press 'w' to start application workflow                      │".to_string()),
        VNode::Text("└──────────────────────────────────────────────────────────────────┘".to_string()),
    ])
}

/// Helper function to format future status for concurrent operations display
fn format_future_status<T, E>(name: &str, future: &rink_core::hooks::future::FutureHandle<T, E>) -> String 
where
    T: std::fmt::Display,
    E: std::fmt::Display,
{
    match future.state() {
        FutureState::Pending => format!("⏳ {}: Loading...", name),
        FutureState::Progress(p) => format!("🔄 {}: {}%", name, (p * 100.0) as u32),
        FutureState::Resolved(data) => format!("✅ {}: {}", name, data),
        FutureState::Error(err) => format!("❌ {}: {}", name, err),
    }
}

/// Helper function to pad content for consistent box formatting
fn pad_content(content: &str, width: usize) -> String {
    content
        .lines()
        .map(|line| format!("{:<width$}", line, width = width))
        .collect::<Vec<_>>()
        .join("│\n│ ")
}

#[cfg(test)]
mod tests {
    use super::*;
    use rink_core::hooks::test_utils::with_test_isolate;

    #[test]
    fn test_future_examples_render() {
        with_test_isolate(|| {
            // Test that all examples can be created without panicking
            let showcase = future_hooks_showcase();
            assert!(matches!(showcase, VNode::Container(_)));
            
            let header = create_header();
            assert!(matches!(header, VNode::Text(_)));
            
            let basic = basic_future_example();
            assert!(matches!(basic, VNode::Container(_)));
        });
    }

    #[test]
    fn test_helper_functions() {
        let padded = pad_content("Hello\nWorld", 10);
        assert!(padded.contains("Hello     "));
        assert!(padded.contains("World     "));
    }
}
