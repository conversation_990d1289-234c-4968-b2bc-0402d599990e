//! Comprehensive use_app hook examples
//!
//! This module demonstrates the professional use_app hook inspired by ink's approach,
//! showcasing application-level state management, lifecycle control, and event handling.

use crossterm::event::{Event, KeyCode};
use ratatui::layout::{Constraint, Direction};
use rink::prelude::*;
use rink_core::VNode;
use std::time::Duration;

/// Simple counter app demonstrating use_app with use_event and exit interception
pub fn simple_counter_app() -> impl Into<VNode> {
    let (app, controller) = use_app();

    // Set up exit interception on first render
    let exit_confirmation_shown = app.field(|state| {
        state
            .get_data::<bool>("exit_confirmation_shown")
            .unwrap_or(false)
    });

    if !exit_confirmation_shown {
        controller.set_data("exit_confirmation_shown", true);

        // Register exit interceptor for confirmation
        controller.set_exit_interceptor(|reason| {
            match reason {
                ExitReason::UserRequested => {
                    // Don't exit immediately, show confirmation instead
                    false
                }
                _ => true, // Allow other exit reasons to proceed
            }
        });
    }

    // Handle events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('q') => {
                let showing_confirmation = app.field(|state| {
                    state
                        .get_data::<bool>("showing_exit_confirmation")
                        .unwrap_or(false)
                });

                if showing_confirmation {
                    // User pressed 'q' again while confirmation is showing - force exit
                    controller.clear_exit_interceptor();
                    controller.exit(ExitReason::UserRequested);
                } else {
                    // First time pressing 'q' - show confirmation
                    controller.set_data("showing_exit_confirmation", true);
                    // Try to exit (will be intercepted)
                    controller.exit(ExitReason::UserRequested);
                }
            }
            KeyCode::Char('n') | KeyCode::Esc => {
                // Cancel exit confirmation
                controller.set_data("showing_exit_confirmation", false);
            }
            KeyCode::Char('y') | KeyCode::Enter => {
                let showing_confirmation = app.field(|state| {
                    state
                        .get_data::<bool>("showing_exit_confirmation")
                        .unwrap_or(false)
                });

                if showing_confirmation {
                    // User confirmed exit
                    controller.clear_exit_interceptor();
                    controller.exit(ExitReason::UserRequested);
                }
            }
            KeyCode::Up | KeyCode::Char('+') => {
                let current = app.field(|state| state.get_data::<i32>("counter").unwrap_or(0));
                controller.set_data("counter", current + 1);
            }
            KeyCode::Down | KeyCode::Char('-') => {
                let current = app.field(|state| state.get_data::<i32>("counter").unwrap_or(0));
                controller.set_data("counter", current - 1);
            }
            KeyCode::Char('r') => {
                controller.set_data("counter", 0i32);
            }
            _ => {}
        }
    }

    // Check if app should exit
    if app.should_exit() {
        return rsx! {
            <Block
                title="Goodbye!"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Red)}
            >
                <Paragraph alignment={Alignment::Center}>
                    {"Thanks for using the app!"}
                </Paragraph>
            </Block>
        };
    }

    // Check if showing exit confirmation
    let showing_confirmation = app.field(|state| {
        state
            .get_data::<bool>("showing_exit_confirmation")
            .unwrap_or(false)
    });

    if showing_confirmation {
        return rsx! {
            <Block
                title="Exit Confirmation"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Paragraph
                    style={Style::default().fg(Color::White)}
                    alignment={Alignment::Center}
                >
                    {"Are you sure you want to quit?\n\n"}
                    {"Press 'y' or Enter to confirm\n"}
                    {"Press 'n' or Esc to cancel\n"}
                    {"Press 'q' again to force quit"}
                </Paragraph>
            </Block>
        };
    }

    // Get counter value from app state
    let counter = app.field(|state| state.get_data::<i32>("counter").unwrap_or(0));

    rsx! {
        <Block
            title="Simple Counter App (with Exit Confirmation)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!(
                    "Counter: {}\n\nControls:\n↑/+ : Increment\n↓/- : Decrement\nr : Reset\nq : Quit (with confirmation)",
                    counter
                )}
            </Paragraph>
        </Block>
    }
}

/// Advanced exit interception example demonstrating various scenarios
pub fn exit_interception_demo() -> impl Into<VNode> {
    let (app, controller) = use_app();

    // Initialize exit interception on first render
    let initialized = app.field(|state| {
        state
            .get_data::<bool>("exit_demo_initialized")
            .unwrap_or(false)
    });

    if !initialized {
        controller.set_data("exit_demo_initialized", true);
        controller.set_data("unsaved_changes", false);
        controller.set_data("save_in_progress", false);

        // Set up sophisticated exit interceptor
        controller.set_exit_interceptor(|reason| {
            // This would normally access app state, but for demo we'll use simple logic
            match reason {
                ExitReason::UserRequested => {
                    // In a real app, you'd check for unsaved changes here
                    false // Always show confirmation for user exits
                }
                ExitReason::Error(_) => true,       // Allow error exits
                ExitReason::SystemShutdown => true, // Allow system shutdowns
                _ => false,                         // Intercept other exits for confirmation
            }
        });
    }

    // Handle events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('q') => {
                controller.exit(ExitReason::UserRequested);
            }
            KeyCode::Char('s') => {
                // Simulate saving
                controller.set_data("save_in_progress", true);
                controller.set_data("unsaved_changes", false);
            }
            KeyCode::Char('e') => {
                // Simulate making changes
                controller.set_data("unsaved_changes", true);
            }
            KeyCode::Char('f') => {
                // Force exit (bypass interceptor)
                controller.clear_exit_interceptor();
                controller.exit(ExitReason::UserRequested);
            }
            KeyCode::Char('c') => {
                // Simulate crash/error
                controller.exit(ExitReason::Error("Simulated error".to_string()));
            }
            _ => {}
        }
    }

    // Check if app should exit
    if app.should_exit() {
        let exit_reason = app.exit_reason();
        return rsx! {
            <Block
                title="Application Exiting"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Red)}
            >
                <Paragraph alignment={Alignment::Center}>
                    {format!("Goodbye!\nExit reason: {:?}", exit_reason)}
                </Paragraph>
            </Block>
        };
    }

    // Get current state
    let unsaved_changes =
        app.field(|state| state.get_data::<bool>("unsaved_changes").unwrap_or(false));
    let save_in_progress =
        app.field(|state| state.get_data::<bool>("save_in_progress").unwrap_or(false));

    let status = if save_in_progress {
        "Saving..."
    } else if unsaved_changes {
        "Unsaved changes"
    } else {
        "All saved"
    };

    rsx! {
        <Block
            title="Exit Interception Demo"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Status: {}\n\n\
                    Commands:\n\
                    e - Make changes (creates unsaved state)\n\
                    s - Save changes\n\
                    q - Quit (will be intercepted)\n\
                    f - Force quit (bypass interceptor)\n\
                    c - Simulate crash (error exit)\n\n\
                    Exit interception is active!\n\
                    Try pressing 'q' to see it in action.",
                    status
                )}
            </Paragraph>
        </Block>
    }
}

/// Basic application example with lifecycle management
pub fn basic_app_example() -> impl Into<VNode> {
    let (app, controller) = use_app();

    // Handle global application events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('q') => {
                controller.exit(ExitReason::UserRequested);
            }
            KeyCode::Char('p') => {
                if app.lifecycle() == AppLifecycle::Running {
                    controller.pause();
                } else if app.lifecycle() == AppLifecycle::Paused {
                    controller.resume();
                }
            }
            KeyCode::Char('r') => {
                if app.lifecycle() == AppLifecycle::Pausing {
                    controller.complete_pause();
                } else if app.lifecycle() == AppLifecycle::Resuming {
                    controller.complete_resume();
                }
            }
            _ => {}
        }
    }

    // Handle exit state
    if app.should_exit() {
        let reason = app.exit_reason().unwrap_or(ExitReason::Completed);
        return rsx! {
            <Block
                title="Application Shutting Down"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Red)}
            >
                <Paragraph
                    style={Style::default().fg(Color::White)}
                    alignment={Alignment::Center}
                >
                    {format!("Exit Reason: {:?}\n\nGoodbye!", reason)}
                </Paragraph>
            </Block>
        };
    }

    let lifecycle_color = match app.lifecycle() {
        AppLifecycle::Running => Color::Green,
        AppLifecycle::Paused => Color::Yellow,
        AppLifecycle::Pausing | AppLifecycle::Resuming => Color::Blue,
        _ => Color::Gray,
    };

    rsx! {
        <Block
            title="Basic App Example (use_app)"
            borders={Borders::ALL}
            border_style={Style::default().fg(lifecycle_color)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Application Lifecycle: {:?}\n\nControls:\nq : Quit application\np : Pause/Resume\nr : Complete transition\n\nThe border color indicates the current state:\nGreen = Running\nYellow = Paused\nBlue = Transitioning",
                    app.lifecycle()
                )}
            </Paragraph>
        </Block>
    }
}

/// Application with custom configuration and performance monitoring
pub fn configured_app_example() -> impl Into<VNode> {
    let config = AppConfig {
        name: "Professional TUI App".to_string(),
        version: "2.1.0".to_string(),
        debug: true,
        auto_save_interval: Some(Duration::from_secs(30)),
        max_event_queue_size: 1000,
        enable_performance_monitoring: true,
    };

    let (app, controller) = use_app_with_config(config);
    let metrics = app.metrics();
    let config = app.config();

    // Handle events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('q') => {
                controller.exit(ExitReason::UserRequested);
            }
            KeyCode::Char('d') => {
                controller.update_config(|cfg| {
                    cfg.debug = !cfg.debug;
                });
            }
            _ => {}
        }
    }

    if app.should_exit() {
        return rsx! {
            <Block title="Shutting Down" borders={Borders::ALL}>
                <Paragraph>{"Application is shutting down..."}</Paragraph>
            </Block>
        };
    }

    rsx! {
        <Block
            title={format!("{} v{}", config.name, config.version)}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Configuration:\nDebug Mode: {}\nAuto-save: {:?}\nMax Events: {}\nPerformance Monitoring: {}\n\nMetrics:\nRenders: {}\nEvents: {}\nAvg Frame Time: {:.2}ms\nUptime: {:?}\n\nControls:\nq : Quit\nd : Toggle debug mode",
                    config.debug,
                    config.auto_save_interval,
                    config.max_event_queue_size,
                    config.enable_performance_monitoring,
                    metrics.render_count,
                    metrics.event_count,
                    metrics.avg_frame_time_ms,
                    metrics.start_time.map(|t| t.elapsed())
                )}
            </Paragraph>
        </Block>
    }
}

/// Application with global data storage
pub fn data_storage_app_example() -> impl Into<VNode> {
    let (app, controller) = use_app();

    #[derive(Clone, Debug)]
    struct UserSession {
        username: String,
        login_time: std::time::Instant,
        score: u32,
    }

    // Handle events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('q') => {
                controller.exit(ExitReason::UserRequested);
            }
            KeyCode::Char('l') => {
                // Simulate login
                let session = UserSession {
                    username: "player1".to_string(),
                    login_time: std::time::Instant::now(),
                    score: 0,
                };
                controller.set_data("user_session", session);
            }
            KeyCode::Char('s') => {
                // Update score
                if let Some(mut session) =
                    app.field(|state| state.get_data::<UserSession>("user_session"))
                {
                    session.score += 10;
                    controller.set_data("user_session", session);
                }
            }
            KeyCode::Char('o') => {
                // Logout
                controller.remove_data("user_session");
            }
            _ => {}
        }
    }

    if app.should_exit() {
        return rsx! {
            <Block title="Goodbye!" borders={Borders::ALL}>
                <Paragraph>{"Thanks for playing!"}</Paragraph>
            </Block>
        };
    }

    let session_info = app.field(|state| {
        if let Some(session) = state.get_data::<UserSession>("user_session") {
            format!(
                "Logged in as: {}\nScore: {}\nSession time: {:.1}s",
                session.username,
                session.score,
                session.login_time.elapsed().as_secs_f32()
            )
        } else {
            "Not logged in".to_string()
        }
    });

    let is_logged_in = app.field(|state| state.get_data::<UserSession>("user_session").is_some());

    let border_color = if is_logged_in {
        Color::Green
    } else {
        Color::Red
    };

    rsx! {
        <Block
            title="Data Storage Example (use_app)"
            borders={Borders::ALL}
            border_style={Style::default().fg(border_color)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Global Data Storage Demo\n\n{}\n\nControls:\nl : Login\ns : Increase score (+10)\no : Logout\nq : Quit\n\nBorder color indicates login status:\nGreen = Logged in\nRed = Not logged in",
                    session_info
                )}
            </Paragraph>
        </Block>
    }
}

/// Multi-component application sharing state
pub fn shared_state_app_example() -> impl Into<VNode> {
    let (app, controller) = use_app();

    // Handle global events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('q') => {
                controller.exit(ExitReason::UserRequested);
            }
            KeyCode::Char('i') => {
                let current = app.field(|state| state.get_data::<u32>("counter").unwrap_or(0));
                controller.set_data("counter", current + 1);
            }
            KeyCode::Char('d') => {
                let current = app.field(|state| state.get_data::<u32>("counter").unwrap_or(0));
                if current > 0 {
                    controller.set_data("counter", current - 1);
                }
            }
            KeyCode::Char('r') => {
                controller.set_data("counter", 0u32);
            }
            _ => {}
        }
    }

    if app.should_exit() {
        return rsx! {
            <Block title="Application Closed" borders={Borders::ALL}>
                <Paragraph>{"Application has been closed."}</Paragraph>
            </Block>
        };
    }

    // This demonstrates how multiple components can share the same app state
    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={[
                Constraint::Percentage(50),
                Constraint::Percentage(50)
            ]}
        >
            {header_component()}
            {counter_component()}
        </Layout>
    }
}

fn header_component() -> VNode {
    let (app, _) = use_app();
    let config = app.config();
    let metrics = app.metrics();

    rsx! {
        <Block
            title="Header Component"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!(
                    "{} - Renders: {} | Events: {}",
                    config.name,
                    metrics.render_count,
                    metrics.event_count
                )}
            </Paragraph>
        </Block>
    }
}

fn counter_component() -> VNode {
    let (app, _) = use_app();
    let counter = app.field(|state| state.get_data::<u32>("counter").unwrap_or(0));

    rsx! {
        <Block
            title="Counter Component"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!(
                    "Shared Counter: {}\n\nControls:\ni : Increment\nd : Decrement\nr : Reset\nq : Quit",
                    counter
                )}
            </Paragraph>
        </Block>
    }
}

/// Main function to run all use_app examples
pub fn run_use_app_examples() -> Result<(), Box<dyn std::error::Error>> {
    println!("Running use_app examples...");
    println!("Press 'q' to quit any example");

    // You can uncomment any of these to run individual examples:
    render(simple_counter_app)?;
    // render(basic_app_example)?;
    // render(configured_app_example)?;
    // render(data_storage_app_example)?;
    // render(shared_state_app_example)?;

    Ok(())
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    run_use_app_examples()
}
