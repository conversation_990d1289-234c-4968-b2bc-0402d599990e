[package]
name = "rink_examples"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "simple_demo"
path = "src/simple_demo.rs"

[[bin]]
name = "dashboard_demo"
path = "src/dashboard_demo.rs"

[[bin]]
name = "comprehensive_demo"
path = "src/comprehensive_demo.rs"

# Hook examples
[[bin]]
name = "use_state_example"
path = "src/hooks/use_state_example.rs"

[[bin]]
name = "use_effect_example"
path = "src/hooks/use_effect_example.rs"

[[bin]]
name = "use_async_effect_example"
path = "src/hooks/use_async_effect_example.rs"

[[bin]]
name = "use_reducer_example"
path = "src/hooks/use_reducer_example.rs"

[[bin]]
name = "use_interval_example"
path = "src/hooks/use_interval_example.rs"

[[bin]]
name = "use_async_interval_example"
path = "src/hooks/use_async_interval_example.rs"

[[bin]]
name = "use_event_example"
path = "src/hooks/use_event_example.rs"

[[bin]]
name = "use_app_example"
path = "src/hooks/use_app_example.rs"

[[bin]]
name = "use_future_example"
path = "src/hooks/use_future_example.rs"

[[bin]]
name = "use_context_example"
path = "src/hooks/use_context_example.rs"

[dependencies]
rink = { workspace = true }
rink_core = { workspace = true }
ratatui = { workspace = true }
tokio = { workspace = true, features = ["full"] }
crossterm = { workspace = true }
fastrand = "2.3.0"
