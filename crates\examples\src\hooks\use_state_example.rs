//! Comprehensive use_state hook examples
//!
//! This module demonstrates various patterns and use cases for the use_state hook,
//! including primitive types, complex state objects, and functional updates.

use crossterm::event::{Event, KeyCode};
use ratatui::style::Modifier;
use rink::prelude::*;

/// Simple counter example demonstrating basic use_state functionality
pub fn counter_example() -> impl Into<VNode> {
    let (count, set_count) = use_state(0);

    // Handle keyboard events for increment/decrement
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('+') | KeyCode::Char('=') => {
                set_count.update(|prev| prev + 1);
            }
            KeyCode::Char('-') => {
                set_count.update(|prev| prev - 1);
            }
            KeyCode::Char('r') => {
                set_count.set(0); // Reset to zero
            }
            _ => {}
        }
    }

    rsx! {
        <Block
            title="Counter Example (use_state)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!("Count: {}\n\nControls:\n+ or = : Increment\n- : Decrement\nr : Reset", count.get())}
            </Paragraph>
        </Block>
    }
}

/// Complex state example with a custom struct
#[derive(Clone, Debug)]
pub struct UserProfile {
    pub name: String,
    pub age: u32,
    pub email: String,
    pub is_active: bool,
}

impl Default for UserProfile {
    fn default() -> Self {
        Self {
            name: "John Doe".to_string(),
            age: 25,
            email: "<EMAIL>".to_string(),
            is_active: true,
        }
    }
}

pub fn complex_state_example() -> impl Into<VNode> {
    let (profile, set_profile) = use_state(UserProfile::default());

    // Handle keyboard events for profile updates
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('n') => {
                set_profile.update(|prev| UserProfile {
                    name: format!("{} Updated", prev.name),
                    ..prev
                });
            }
            KeyCode::Char('a') => {
                set_profile.update(|prev| UserProfile {
                    age: prev.age + 1,
                    ..prev
                });
            }
            KeyCode::Char('t') => {
                set_profile.update(|prev| UserProfile {
                    is_active: !prev.is_active,
                    ..prev
                });
            }
            KeyCode::Char('r') => {
                set_profile.set(UserProfile::default());
            }
            _ => {}
        }
    }

    let current_profile = profile.get();
    let status = if current_profile.is_active {
        "Active"
    } else {
        "Inactive"
    };

    rsx! {
        <Block
            title="Complex State Example (use_state)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "User Profile:\n\nName: {}\nAge: {}\nEmail: {}\nStatus: {}\n\nControls:\nn : Update name\na : Increment age\nt : Toggle status\nr : Reset profile",
                    current_profile.name,
                    current_profile.age,
                    current_profile.email,
                    status
                )}
            </Paragraph>
        </Block>
    }
}

/// Multiple state variables example
pub fn multiple_state_example() -> impl Into<VNode> {
    let (text, set_text) = use_state("Hello".to_string());
    let (color_index, set_color_index) = use_state(0usize);
    let (is_bold, set_is_bold) = use_state(false);

    let colors = [
        Color::White,
        Color::Red,
        Color::Green,
        Color::Blue,
        Color::Yellow,
        Color::Magenta,
        Color::Cyan,
    ];
    let current_color = colors[color_index.get() % colors.len()];

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('c') => {
                set_color_index.update(|prev| (prev + 1) % colors.len());
            }
            KeyCode::Char('b') => {
                set_is_bold.update(|prev| !prev);
            }
            KeyCode::Char('t') => {
                set_text.update(|prev| {
                    if prev == "Hello" {
                        "World!".to_string()
                    } else {
                        "Hello".to_string()
                    }
                });
            }
            _ => {}
        }
    }

    let mut style = Style::default().fg(current_color);
    if is_bold.get() {
        style = style.add_modifier(Modifier::BOLD);
    }

    rsx! {
        <Block
            title="Multiple State Variables (use_state)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Paragraph
                style={style}
                alignment={Alignment::Center}
            >
                {format!("{}\n\nControls:\nc : Change color\nb : Toggle bold\nt : Toggle text", text.get())}
            </Paragraph>
        </Block>
    }
}

/// State with field access example
pub fn field_access_example() -> impl Into<VNode> {
    #[derive(Clone)]
    struct AppSettings {
        theme: String,
        font_size: u16,
        auto_save: bool,
        notifications: bool,
    }

    let (settings, set_settings) = use_state(AppSettings {
        theme: "Dark".to_string(),
        font_size: 14,
        auto_save: true,
        notifications: false,
    });

    // Efficiently access specific fields without cloning entire state
    let theme = settings.field(|s| s.theme.clone());
    let font_size = settings.field(|s| s.font_size);
    let auto_save = settings.field(|s| s.auto_save);
    let notifications = settings.field(|s| s.notifications);

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('1') => {
                set_settings.update(|prev| AppSettings {
                    theme: if prev.theme == "Dark" {
                        "Light".to_string()
                    } else {
                        "Dark".to_string()
                    },
                    ..prev
                });
            }
            KeyCode::Char('2') => {
                set_settings.update(|prev| AppSettings {
                    font_size: if prev.font_size >= 20 {
                        12
                    } else {
                        prev.font_size + 2
                    },
                    ..prev
                });
            }
            KeyCode::Char('3') => {
                set_settings.update(|prev| AppSettings {
                    auto_save: !prev.auto_save,
                    ..prev
                });
            }
            KeyCode::Char('4') => {
                set_settings.update(|prev| AppSettings {
                    notifications: !prev.notifications,
                    ..prev
                });
            }
            _ => {}
        }
    }

    rsx! {
        <Block
            title="Field Access Example (use_state)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "App Settings:\n\nTheme: {}\nFont Size: {}px\nAuto Save: {}\nNotifications: {}\n\nControls:\n1 : Toggle theme\n2 : Change font size\n3 : Toggle auto save\n4 : Toggle notifications",
                    theme,
                    font_size,
                    if auto_save { "Enabled" } else { "Disabled" },
                    if notifications { "Enabled" } else { "Disabled" }
                )}
            </Paragraph>
        </Block>
    }
}

/// Main function to run all use_state examples
pub fn run_use_state_examples() -> Result<(), Box<dyn std::error::Error>> {
    println!("Running use_state examples...");
    println!("Press 'q' to quit any example");

    // You can uncomment any of these to run individual examples:
    // render(counter_example)?;
    // render(complex_state_example)?;
    // render(multiple_state_example)?;
    render(field_access_example)?;

    Ok(())
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    run_use_state_examples()
}
