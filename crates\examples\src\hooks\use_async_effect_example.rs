//! Comprehensive use_async_effect hook examples
//!
//! This module demonstrates various patterns for the use_async_effect hook,
//! including async effects with dependencies, async cleanup functions, and async operations.

use crossterm::event::{Event, KeyCode};
use rink::prelude::*;
use rink_core::hooks::effect::use_async_effect;
use std::time::{Duration, SystemTime, UNIX_EPOCH};

/// Basic async effect example
pub fn basic_async_effect_example() -> impl Into<VNode> {
    let (status, set_status) = use_state("Initializing...".to_string());
    let (operation_count, set_operation_count) = use_state(0);

    // Async effect runs on every render
    use_async_effect::<(), _, _, _, _>(
        {
            let set_status = set_status.clone();
            let set_operation_count = set_operation_count.clone();
            move || async move {
                set_status.set("Running async operation...".to_string());

                // Simulate async work
                tokio::time::sleep(Duration::from_millis(100)).await;

                let timestamp = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();

                set_status.set(format!("Async operation completed at {}", timestamp));
                set_operation_count.update(|prev| prev + 1);

                None::<fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send>>> // No cleanup needed
            }
        },
        None, // No dependencies - runs on every render
    );

    rsx! {
        <Block
            title="Basic Async Effect Example (use_async_effect)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {format!(
                    "Status: {}\nOperation Count: {}\n\nThis effect runs on every render",
                    status.get(),
                    operation_count.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Async effect with dependencies - simulates data fetching
pub fn async_data_fetching_example() -> impl Into<VNode> {
    let (user_id, set_user_id) = use_state(1);
    let (user_data, set_user_data) = use_state("Loading...".to_string());
    let (is_loading, set_is_loading) = use_state(false);
    let (fetch_count, set_fetch_count) = use_state(0);

    // Async effect runs only when user_id changes
    use_async_effect(
        {
            let user_id_value = user_id.get();
            let set_user_data = set_user_data.clone();
            let set_is_loading = set_is_loading.clone();
            let set_fetch_count = set_fetch_count.clone();
            move || async move {
                set_is_loading.set(true);
                set_user_data.set("Fetching user data...".to_string());

                // Simulate async API call
                tokio::time::sleep(Duration::from_millis(500)).await;

                let data = format!(
                    "User {} Profile:\nName: User {}\nEmail: user{}@example.com\nJoined: 2024-01-{:02}",
                    user_id_value,
                    user_id_value,
                    user_id_value,
                    (user_id_value % 28) + 1
                );

                set_user_data.set(data);
                set_is_loading.set(false);
                set_fetch_count.update(|prev| prev + 1);

                None::<fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send>>>
            }
        },
        user_id.get(), // Effect depends on user_id
    );

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('n') => {
                set_user_id.update(|prev| *prev + 1);
            }
            KeyCode::Char('p') => {
                set_user_id.update(|prev| if *prev > 1 { *prev - 1 } else { 1 });
            }
            _ => {}
        }
    }

    let loading_indicator = if is_loading.get() {
        " [Loading...]"
    } else {
        ""
    };

    rsx! {
        <Block
            title={format!("Async Data Fetching (use_async_effect){}", loading_indicator)}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Current User ID: {}\nFetch Count: {}\n\n{}\n\nControls:\nn : Next user\np : Previous user",
                    user_id.get(),
                    fetch_count.get(),
                    user_data.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Async effect with cleanup function
pub fn async_effect_with_cleanup_example() -> impl Into<VNode> {
    let (is_monitoring, set_is_monitoring) = use_state(false);
    let (monitor_data, set_monitor_data) = use_state("Not monitoring".to_string());
    let (cleanup_count, set_cleanup_count) = use_state(0);

    // Async effect with async cleanup
    use_async_effect(
        {
            let is_active = is_monitoring.get();
            let set_monitor_data = set_monitor_data.clone();
            let set_cleanup_count = set_cleanup_count.clone();
            move || async move {
                if is_active {
                    set_monitor_data.set("Starting monitoring...".to_string());

                    // Simulate async setup
                    tokio::time::sleep(Duration::from_millis(200)).await;

                    set_monitor_data.set("Monitoring active - collecting data...".to_string());

                    // Return async cleanup function
                    Some(move || {
                        Box::pin(async move {
                            // Simulate async cleanup
                            tokio::time::sleep(Duration::from_millis(100)).await;
                            set_cleanup_count.update(|prev| prev + 1);
                        })
                            as std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send>>
                    })
                } else {
                    set_monitor_data.set("Not monitoring".to_string());
                    None
                }
            }
        },
        is_monitoring.get(), // Effect depends on monitoring state
    );

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('m') => {
                set_is_monitoring.update(|prev| !prev);
            }
            KeyCode::Char('r') => {
                set_cleanup_count.set(0);
            }
            _ => {}
        }
    }

    let status = if is_monitoring.get() { "ON" } else { "OFF" };

    rsx! {
        <Block
            title="Async Effect with Cleanup (use_async_effect)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Monitoring: {}\nCleanup Count: {}\n\nStatus:\n{}\n\nControls:\nm : Toggle monitoring\nr : Reset cleanup count",
                    status,
                    cleanup_count.get(),
                    monitor_data.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Complex async effect with multiple async operations
pub fn complex_async_effect_example() -> impl Into<VNode> {
    let (config, set_config) = use_state("default".to_string());
    let (environment, set_environment) = use_state("dev".to_string());
    let (system_status, set_system_status) = use_state("Initializing...".to_string());
    let (last_update, set_last_update) = use_state(0u64);

    // Complex async effect that depends on both config and environment
    use_async_effect(
        {
            let config_value = config.get();
            let env_value = environment.get();
            let set_system_status = set_system_status.clone();
            let set_last_update = set_last_update.clone();
            move || async move {
                set_system_status.set("Loading configuration...".to_string());

                // Simulate loading config
                tokio::time::sleep(Duration::from_millis(200)).await;

                set_system_status.set("Connecting to services...".to_string());

                // Simulate service connections
                tokio::time::sleep(Duration::from_millis(300)).await;

                set_system_status.set("Validating environment...".to_string());

                // Simulate environment validation
                tokio::time::sleep(Duration::from_millis(150)).await;

                let timestamp = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();

                set_system_status.set(format!(
                    "System Ready!\nConfig: {}\nEnvironment: {}\nServices: Connected\nDatabase: Online",
                    config_value,
                    env_value
                ));
                set_last_update.set(timestamp);

                None::<fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send>>>
            }
        },
        (config.get(), environment.get()), // Effect depends on both config and environment
    );

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('c') => {
                let configs = ["default", "production", "testing", "staging"];
                let current = config.get();
                let current_index = configs.iter().position(|&c| c == current).unwrap_or(0);
                let next_index = (current_index + 1) % configs.len();
                set_config.set(configs[next_index].to_string());
            }
            KeyCode::Char('e') => {
                let envs = ["dev", "staging", "prod"];
                let current = environment.get();
                let current_index = envs.iter().position(|&e| e == current).unwrap_or(0);
                let next_index = (current_index + 1) % envs.len();
                set_environment.set(envs[next_index].to_string());
            }
            _ => {}
        }
    }

    rsx! {
        <Block
            title="Complex Async Effect (use_async_effect)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Config: {}\nEnvironment: {}\nLast Update: {}\n\nSystem Status:\n{}\n\nControls:\nc : Change config\ne : Change environment",
                    config.get(),
                    environment.get(),
                    last_update.get(),
                    system_status.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Main function to run all use_async_effect examples
pub fn run_use_async_effect_examples() -> Result<(), Box<dyn std::error::Error>> {
    println!("Running use_async_effect examples...");
    println!("Press 'q' to quit any example");
    println!("Note: These examples require a tokio runtime");

    // You can uncomment any of these to run individual examples:
    // render(basic_async_effect_example)?;
    // render(async_data_fetching_example)?;
    // render(async_effect_with_cleanup_example)?;
    render(complex_async_effect_example)?;

    Ok(())
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    render_async(complex_async_effect_example).await
}
