//! # Context and Reducer Integration Example
//!
//! This example demonstrates professional state management patterns using `use_context`
//! and `use_reducer` hooks in a task management dashboard application.
//!
//! ## Features Demonstrated
//!
//! - **Context Provider Hierarchy**: Nested contexts for different application concerns
//! - **Complex State Management**: Using reducers for predictable state updates
//! - **Component Communication**: Sharing state and actions across component tree
//! - **Performance Optimization**: Selective re-rendering with context splitting
//! - **Real-world Patterns**: Task management with filtering, sorting, and validation
//!
//! ## Architecture
//!
//! ```text
//! AppProvider (user auth, theme)
//! └── TaskProvider (task state, dispatch)
//!     ├── Header (user info, stats)
//!     ├── TaskList (filtered tasks)
//!     ├── TaskForm (add/edit tasks)
//!     └── Footer (actions, status)
//! ```
//!
//! ## Usage
//!
//! ```bash
//! cargo run --bin use_context_example
//! ```

use crossterm::event::{Event, KeyCode};
use ratatui::{
    layout::{Constraint, Direction},
    style::{Color, Style},
    widgets::{Block, Borders, Paragraph, Wrap},
};
use rink::{render, rsx};
use rink_core::{
    VNode,
    hooks::{
        context::use_context_provider,
        event::use_event,
        reducer::{DispatchFn, use_reducer},
    },
};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};

/// Application entry point
pub fn main() -> Result<(), Box<dyn std::error::Error>> {
    render(build_ui)
}

// ============================================================================
// Domain Models
// ============================================================================

/// Task priority levels
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Priority {
    Low,
    Medium,
    High,
    Critical,
}

impl Priority {
    pub fn as_str(&self) -> &'static str {
        match self {
            Priority::Low => "Low",
            Priority::Medium => "Medium",
            Priority::High => "High",
            Priority::Critical => "Critical",
        }
    }

    pub fn color(&self) -> Color {
        match self {
            Priority::Low => Color::Green,
            Priority::Medium => Color::Yellow,
            Priority::High => Color::Red,
            Priority::Critical => Color::Magenta,
        }
    }
}

/// Task status
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum TaskStatus {
    Todo,
    InProgress,
    Review,
    Done,
}

impl TaskStatus {
    pub fn as_str(&self) -> &'static str {
        match self {
            TaskStatus::Todo => "Todo",
            TaskStatus::InProgress => "In Progress",
            TaskStatus::Review => "Review",
            TaskStatus::Done => "Done",
        }
    }

    pub fn color(&self) -> Color {
        match self {
            TaskStatus::Todo => Color::Gray,
            TaskStatus::InProgress => Color::Blue,
            TaskStatus::Review => Color::Yellow,
            TaskStatus::Done => Color::Green,
        }
    }
}

/// Individual task
#[derive(Debug, Clone)]
pub struct Task {
    pub id: u32,
    pub title: String,
    pub description: String,
    pub priority: Priority,
    pub status: TaskStatus,
    pub assignee: String,
    pub created_at: u64,
    pub updated_at: u64,
}

impl Task {
    pub fn new(id: u32, title: String, description: String, assignee: String) -> Self {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        Self {
            id,
            title,
            description,
            priority: Priority::Medium,
            status: TaskStatus::Todo,
            assignee,
            created_at: now,
            updated_at: now,
        }
    }
}

// ============================================================================
// Application Context Types
// ============================================================================

/// User authentication and profile context
#[derive(Debug, Clone)]
pub struct UserContext {
    pub username: String,
    pub role: String,
    pub permissions: Vec<String>,
    pub theme: String,
}

impl Default for UserContext {
    fn default() -> Self {
        Self {
            username: "John Doe".to_string(),
            role: "Developer".to_string(),
            permissions: vec!["read".to_string(), "write".to_string(), "admin".to_string()],
            theme: "dark".to_string(),
        }
    }
}

/// Task filter and view settings
#[derive(Debug, Default, Clone, PartialEq)]
pub struct TaskFilter {
    pub status: Option<TaskStatus>,
    pub priority: Option<Priority>,
    pub assignee: Option<String>,
    pub search_term: String,
}

// ============================================================================
// Task State and Reducer
// ============================================================================

/// Task management state
#[derive(Debug, Clone)]
pub struct TaskState {
    pub tasks: HashMap<u32, Task>,
    pub filter: TaskFilter,
    pub selected_task_id: Option<u32>,
    pub editing_task_id: Option<u32>,
    pub next_id: u32,
    pub view_mode: ViewMode,
    pub error_message: Option<String>,
}

/// View modes for the task dashboard
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum ViewMode {
    List,
    Board,
    Form,
    Details,
}

impl ViewMode {
    pub fn as_str(&self) -> &'static str {
        match self {
            ViewMode::List => "List View",
            ViewMode::Board => "Board View",
            ViewMode::Form => "Task Form",
            ViewMode::Details => "Task Details",
        }
    }
}

impl Default for TaskState {
    fn default() -> Self {
        let mut tasks = HashMap::new();

        // Add some sample tasks
        let sample_tasks = vec![
            Task {
                id: 1,
                title: "Implement user authentication".to_string(),
                description: "Add login/logout functionality with JWT tokens".to_string(),
                priority: Priority::High,
                status: TaskStatus::InProgress,
                assignee: "Alice".to_string(),
                created_at: 1640995200, // 2022-01-01
                updated_at: 1640995200,
            },
            Task {
                id: 2,
                title: "Design database schema".to_string(),
                description: "Create tables for users, tasks, and projects".to_string(),
                priority: Priority::Medium,
                status: TaskStatus::Review,
                assignee: "Bob".to_string(),
                created_at: 1641081600, // 2022-01-02
                updated_at: 1641081600,
            },
            Task {
                id: 3,
                title: "Write API documentation".to_string(),
                description: "Document all REST endpoints with examples".to_string(),
                priority: Priority::Low,
                status: TaskStatus::Todo,
                assignee: "Charlie".to_string(),
                created_at: 1641168000, // 2022-01-03
                updated_at: 1641168000,
            },
        ];

        for task in sample_tasks {
            tasks.insert(task.id, task);
        }

        Self {
            tasks,
            filter: TaskFilter::default(),
            selected_task_id: Some(1),
            editing_task_id: None,
            next_id: 4,
            view_mode: ViewMode::List,
            error_message: None,
        }
    }
}

/// Actions for task state management
#[derive(Debug, Clone)]
pub enum TaskAction {
    // Task CRUD operations
    CreateTask {
        title: String,
        description: String,
        assignee: String,
    },
    UpdateTask {
        id: u32,
        task: Task,
    },
    DeleteTask {
        id: u32,
    },

    // Task status management
    ChangeTaskStatus {
        id: u32,
        status: TaskStatus,
    },
    ChangeTaskPriority {
        id: u32,
        priority: Priority,
    },

    // Selection and navigation
    SelectTask {
        id: Option<u32>,
    },
    StartEditingTask {
        id: u32,
    },
    StopEditing,

    // Filtering and search
    SetFilter {
        filter: TaskFilter,
    },
    ClearFilter,
    SetSearchTerm {
        term: String,
    },

    // View management
    ChangeViewMode {
        mode: ViewMode,
    },

    // Error handling
    SetError {
        message: String,
    },
    ClearError,
}

fn task_reducer(state: TaskState, action: TaskAction) -> TaskState {
    let mut new_state = state.clone();
    new_state.error_message = None; // Clear errors on new actions

    match action {
        TaskAction::CreateTask {
            title,
            description,
            assignee,
        } => {
            if title.trim().is_empty() {
                new_state.error_message = Some("Task title cannot be empty".to_string());
                return new_state;
            }

            let task = Task::new(new_state.next_id, title, description, assignee);
            new_state.tasks.insert(task.id, task.clone());
            new_state.next_id += 1;
            new_state.selected_task_id = Some(task.id);
            new_state.view_mode = ViewMode::List;
        }

        TaskAction::UpdateTask { id, task } => {
            if let Some(existing_task) = new_state.tasks.get_mut(&id) {
                *existing_task = task;
                existing_task.updated_at = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
            } else {
                new_state.error_message = Some(format!("Task {} not found", id));
            }
        }

        TaskAction::DeleteTask { id } => {
            if new_state.tasks.remove(&id).is_some() {
                if new_state.selected_task_id == Some(id) {
                    new_state.selected_task_id = new_state.tasks.keys().next().copied();
                }
                if new_state.editing_task_id == Some(id) {
                    new_state.editing_task_id = None;
                }
            } else {
                new_state.error_message = Some(format!("Task {} not found", id));
            }
        }

        TaskAction::ChangeTaskStatus { id, status } => {
            if let Some(task) = new_state.tasks.get_mut(&id) {
                task.status = status;
                task.updated_at = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
            } else {
                new_state.error_message = Some(format!("Task {} not found", id));
            }
        }

        TaskAction::ChangeTaskPriority { id, priority } => {
            if let Some(task) = new_state.tasks.get_mut(&id) {
                task.priority = priority;
                task.updated_at = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
            } else {
                new_state.error_message = Some(format!("Task {} not found", id));
            }
        }

        TaskAction::SelectTask { id } => {
            new_state.selected_task_id = id;
        }

        TaskAction::StartEditingTask { id } => {
            if new_state.tasks.contains_key(&id) {
                new_state.editing_task_id = Some(id);
                new_state.view_mode = ViewMode::Form;
            } else {
                new_state.error_message = Some(format!("Task {} not found", id));
            }
        }

        TaskAction::StopEditing => {
            new_state.editing_task_id = None;
            new_state.view_mode = ViewMode::List;
        }

        TaskAction::SetFilter { filter } => {
            new_state.filter = filter;
        }

        TaskAction::ClearFilter => {
            new_state.filter = TaskFilter::default();
        }

        TaskAction::SetSearchTerm { term } => {
            new_state.filter.search_term = term;
        }

        TaskAction::ChangeViewMode { mode } => {
            new_state.view_mode = mode;
            if mode != ViewMode::Form {
                new_state.editing_task_id = None;
            }
        }

        TaskAction::SetError { message } => {
            new_state.error_message = Some(message);
        }

        TaskAction::ClearError => {
            new_state.error_message = None;
        }
    }

    new_state
}

// ============================================================================
// Context Types for Provider/Consumer Pattern
// ============================================================================

/// Task context that provides state and dispatch function
#[derive(Clone)]
pub struct TaskContext {
    pub state: TaskState,
    pub dispatch: DispatchFn<TaskAction>,
}

// ============================================================================
// Main UI Component
// ============================================================================

/// Main application UI with context providers
fn build_ui() -> VNode {
    // Provide user context at the top level
    let user_context = use_context_provider(UserContext::default);

    // Provide task state and reducer context
    let (task_state, task_dispatch) = use_reducer(task_reducer, TaskState::default());

    let task_context = use_context_provider(|| TaskContext {
        state: task_state.get().clone(),
        dispatch: task_dispatch.clone(),
    });

    // Handle global keyboard shortcuts
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('q') => {
                // Quit application
                
            }
            KeyCode::Char('1') => {
                task_dispatch.call(TaskAction::ChangeViewMode {
                    mode: ViewMode::List,
                });
            }
            KeyCode::Char('2') => {
                task_dispatch.call(TaskAction::ChangeViewMode {
                    mode: ViewMode::Board,
                });
            }
            KeyCode::Char('3') => {
                task_dispatch.call(TaskAction::ChangeViewMode {
                    mode: ViewMode::Form,
                });
            }
            KeyCode::Char('4') => {
                task_dispatch.call(TaskAction::ChangeViewMode {
                    mode: ViewMode::Details,
                });
            }
            KeyCode::Char('n') => {
                task_dispatch.call(TaskAction::ChangeViewMode {
                    mode: ViewMode::Form,
                });
                task_dispatch.call(TaskAction::StopEditing);
            }
            KeyCode::Char('c') => {
                task_dispatch.call(TaskAction::ClearFilter);
            }
            KeyCode::Esc => {
                task_dispatch.call(TaskAction::StopEditing);
                task_dispatch.call(TaskAction::ClearError);
            }
            _ => {}
        }
    }

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={[
                Constraint::Length(3),    // Header: fixed height
                Constraint::Min(10),      // Content: flexible, minimum 10 lines
                Constraint::Length(3)     // Footer: fixed height
            ]}
        >
            {render_header(&user_context, &task_context)}
            {render_content(&task_context)}
            {render_footer(&task_context)}
        </Layout>
    }
}

// ============================================================================
// UI Rendering Components
// ============================================================================

/// Renders the application header with user info and task statistics
fn render_header(user_context: &UserContext, task_context: &TaskContext) -> VNode {
    let task_stats = calculate_task_stats(&task_context.state);

    rsx! {
        <Block
            title="Task Management Dashboard"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "👤 {} ({}) | 📊 {} tasks | ✅ {} done | 🔄 {} in progress | 🎯 Current: {}",
                    user_context.username,
                    user_context.role,
                    task_stats.total,
                    task_stats.done,
                    task_stats.in_progress,
                    task_context.state.view_mode.as_str()
                )}
            </Paragraph>
        </Block>
    }
}

/// Renders the main content area based on current view mode
fn render_content(task_context: &TaskContext) -> VNode {
    match task_context.state.view_mode {
        ViewMode::List => render_task_list(task_context),
        ViewMode::Board => render_task_board(task_context),
        ViewMode::Form => render_task_form(task_context),
        ViewMode::Details => render_task_details(task_context),
    }
}

/// Renders the application footer with actions and status
fn render_footer(task_context: &TaskContext) -> VNode {
    let error_text = if let Some(ref error) = task_context.state.error_message {
        format!("❌ Error: {}", error)
    } else {
        "✅ Ready".to_string()
    };

    rsx! {
        <Block
            title="Actions & Status"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "{} | 🔢 1-4: Views | N: New Task | C: Clear Filter | Q: Quit | ESC: Cancel",
                    error_text
                )}
            </Paragraph>
        </Block>
    }
}

/// Renders the task list view with filtering
fn render_task_list(task_context: &TaskContext) -> VNode {
    let filtered_tasks = filter_tasks(&task_context.state);
    let selected_id = task_context.state.selected_task_id;

    // Handle task list navigation
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Up => {
                if let Some(current_id) = selected_id {
                    if let Some(prev_id) = get_previous_task_id(&filtered_tasks, current_id) {
                        task_context
                            .dispatch
                            .call(TaskAction::SelectTask { id: Some(prev_id) });
                    }
                }
            }
            KeyCode::Down => {
                if let Some(current_id) = selected_id {
                    if let Some(next_id) = get_next_task_id(&filtered_tasks, current_id) {
                        task_context
                            .dispatch
                            .call(TaskAction::SelectTask { id: Some(next_id) });
                    }
                } else if let Some(first_task) = filtered_tasks.first() {
                    task_context.dispatch.call(TaskAction::SelectTask {
                        id: Some(first_task.id),
                    });
                }
            }
            KeyCode::Enter => {
                if let Some(id) = selected_id {
                    task_context
                        .dispatch
                        .call(TaskAction::StartEditingTask { id });
                }
            }
            KeyCode::Delete => {
                if let Some(id) = selected_id {
                    task_context.dispatch.call(TaskAction::DeleteTask { id });
                }
            }
            KeyCode::Char('s') => {
                if let Some(id) = selected_id {
                    if let Some(task) = task_context.state.tasks.get(&id) {
                        let next_status = get_next_status(task.status);
                        task_context.dispatch.call(TaskAction::ChangeTaskStatus {
                            id,
                            status: next_status,
                        });
                    }
                }
            }
            KeyCode::Char('p') => {
                if let Some(id) = selected_id {
                    if let Some(task) = task_context.state.tasks.get(&id) {
                        let next_priority = get_next_priority(task.priority);
                        task_context.dispatch.call(TaskAction::ChangeTaskPriority {
                            id,
                            priority: next_priority,
                        });
                    }
                }
            }
            _ => {}
        }
    }

    let task_list_content = if filtered_tasks.is_empty() {
        "📝 No tasks found. Press 'n' to create a new task.".to_string()
    } else {
        filtered_tasks
            .iter()
            .map(|task| {
                let selected_marker = if Some(task.id) == selected_id {
                    "▶ "
                } else {
                    "  "
                };
                let status_icon = match task.status {
                    TaskStatus::Todo => "⭕",
                    TaskStatus::InProgress => "🔄",
                    TaskStatus::Review => "👀",
                    TaskStatus::Done => "✅",
                };
                let priority_icon = match task.priority {
                    Priority::Low => "🟢",
                    Priority::Medium => "🟡",
                    Priority::High => "🔴",
                    Priority::Critical => "🟣",
                };

                format!(
                    "{}{} {} [{}] {} - {}",
                    selected_marker,
                    status_icon,
                    priority_icon,
                    task.assignee,
                    task.title,
                    task.status.as_str()
                )
            })
            .collect::<Vec<_>>()
            .join("\n")
    };

    rsx! {
        <Block
            title="Task List"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {format!(
                    "{}\n\n💡 Navigation: ↑↓ Select | Enter: Edit | Del: Delete | S: Status | P: Priority",
                    task_list_content
                )}
            </Paragraph>
        </Block>
    }
}

/// Renders the task board view (Kanban-style)
fn render_task_board(task_context: &TaskContext) -> VNode {
    let tasks_by_status = group_tasks_by_status(&task_context.state);

    rsx! {
        <Layout
            direction={Direction::Horizontal}
            constraints={[
                Constraint::Percentage(25),
                Constraint::Percentage(25),
                Constraint::Percentage(25),
                Constraint::Percentage(25)
            ]}
        >
            {render_status_column("Todo", &tasks_by_status.get(&TaskStatus::Todo).unwrap_or(&vec![]))}
            {render_status_column("In Progress", &tasks_by_status.get(&TaskStatus::InProgress).unwrap_or(&vec![]))}
            {render_status_column("Review", &tasks_by_status.get(&TaskStatus::Review).unwrap_or(&vec![]))}
            {render_status_column("Done", &tasks_by_status.get(&TaskStatus::Done).unwrap_or(&vec![]))}
        </Layout>
    }
}

/// Renders a single status column for the board view
fn render_status_column(title: &str, tasks: &[&Task]) -> VNode {
    let content = if tasks.is_empty() {
        "No tasks".to_string()
    } else {
        tasks
            .iter()
            .map(|task| {
                let priority_icon = match task.priority {
                    Priority::Low => "🟢",
                    Priority::Medium => "🟡",
                    Priority::High => "🔴",
                    Priority::Critical => "🟣",
                };
                format!("{} {} [{}]", priority_icon, task.title, task.assignee)
            })
            .collect::<Vec<_>>()
            .join("\n")
    };

    rsx! {
        <Block
            title={title.to_string()}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {content}
            </Paragraph>
        </Block>
    }
}

/// Renders the task form for creating/editing tasks
fn render_task_form(task_context: &TaskContext) -> VNode {
    let form_title = if task_context.state.editing_task_id.is_some() {
        "Edit Task"
    } else {
        "Create New Task"
    };

    // For simplicity, we'll show a static form. In a real app, you'd have form state
    let form_content = if let Some(editing_id) = task_context.state.editing_task_id {
        if let Some(task) = task_context.state.tasks.get(&editing_id) {
            format!(
                "📝 Editing Task #{}\n\n\
                Title: {}\n\
                Description: {}\n\
                Assignee: {}\n\
                Priority: {}\n\
                Status: {}\n\n\
                💡 This is a simplified form view.\n\
                In a real app, you'd have editable fields here.",
                task.id,
                task.title,
                task.description,
                task.assignee,
                task.priority.as_str(),
                task.status.as_str()
            )
        } else {
            "Task not found".to_string()
        }
    } else {
        "📝 Create New Task\n\n\
        Title: [Enter title]\n\
        Description: [Enter description]\n\
        Assignee: [Select assignee]\n\
        Priority: Medium\n\
        Status: Todo\n\n\
        💡 This is a simplified form view.\n\
        In a real app, you'd have input fields here.\n\n\
        Press ESC to cancel or Enter to save."
            .to_string()
    };

    // Handle form actions
    if let Some(Event::Key(key)) = use_event() {
        if key.code == KeyCode::Enter {
            if task_context.state.editing_task_id.is_none() {
                // Create new task with sample data
                task_context.dispatch.call(TaskAction::CreateTask {
                    title: "New Task".to_string(),
                    description: "Task created from form".to_string(),
                    assignee: "Current User".to_string(),
                });
            } else {
                // In a real app, you'd save the edited task here
                task_context.dispatch.call(TaskAction::StopEditing);
            }
        }
    }

    rsx! {
        <Block
            title={form_title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {form_content}
            </Paragraph>
        </Block>
    }
}

/// Renders detailed view of the selected task
fn render_task_details(task_context: &TaskContext) -> VNode {
    let content = if let Some(selected_id) = task_context.state.selected_task_id {
        if let Some(task) = task_context.state.tasks.get(&selected_id) {
            format!(
                "📋 Task Details\n\n\
                ID: #{}\n\
                Title: {}\n\
                Description: {}\n\
                Assignee: {}\n\
                Priority: {} {}\n\
                Status: {} {}\n\
                Created: {}\n\
                Updated: {}\n\n\
                💡 Press Enter to edit this task.",
                task.id,
                task.title,
                task.description,
                task.assignee,
                match task.priority {
                    Priority::Low => "🟢",
                    Priority::Medium => "🟡",
                    Priority::High => "🔴",
                    Priority::Critical => "🟣",
                },
                task.priority.as_str(),
                match task.status {
                    TaskStatus::Todo => "⭕",
                    TaskStatus::InProgress => "🔄",
                    TaskStatus::Review => "👀",
                    TaskStatus::Done => "✅",
                },
                task.status.as_str(),
                format_timestamp(task.created_at),
                format_timestamp(task.updated_at)
            )
        } else {
            "Task not found".to_string()
        }
    } else {
        "No task selected. Go to List view and select a task.".to_string()
    };

    // Handle detail view actions
    if let Some(Event::Key(key)) = use_event() {
        if key.code == KeyCode::Enter {
            if let Some(id) = task_context.state.selected_task_id {
                task_context
                    .dispatch
                    .call(TaskAction::StartEditingTask { id });
            }
        }
    }

    rsx! {
        <Block
            title="Task Details"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                wrap={Wrap { trim: true }}
            >
                {content}
            </Paragraph>
        </Block>
    }
}

// ============================================================================
// Utility Functions
// ============================================================================

/// Task statistics for the header display
#[derive(Debug)]
struct TaskStats {
    total: usize,
    done: usize,
    in_progress: usize,
    todo: usize,
    review: usize,
}

/// Calculate task statistics from the current state
fn calculate_task_stats(state: &TaskState) -> TaskStats {
    let tasks: Vec<&Task> = state.tasks.values().collect();

    TaskStats {
        total: tasks.len(),
        done: tasks
            .iter()
            .filter(|t| t.status == TaskStatus::Done)
            .count(),
        in_progress: tasks
            .iter()
            .filter(|t| t.status == TaskStatus::InProgress)
            .count(),
        todo: tasks
            .iter()
            .filter(|t| t.status == TaskStatus::Todo)
            .count(),
        review: tasks
            .iter()
            .filter(|t| t.status == TaskStatus::Review)
            .count(),
    }
}

/// Filter tasks based on current filter settings
fn filter_tasks(state: &TaskState) -> Vec<Task> {
    let mut tasks: Vec<Task> = state.tasks.values().cloned().collect();

    // Apply status filter
    if let Some(status) = state.filter.status {
        tasks.retain(|task| task.status == status);
    }

    // Apply priority filter
    if let Some(priority) = state.filter.priority {
        tasks.retain(|task| task.priority == priority);
    }

    // Apply assignee filter
    if let Some(ref assignee) = state.filter.assignee {
        tasks.retain(|task| task.assignee == *assignee);
    }

    // Apply search term filter
    if !state.filter.search_term.is_empty() {
        let search_term = state.filter.search_term.to_lowercase();
        tasks.retain(|task| {
            task.title.to_lowercase().contains(&search_term)
                || task.description.to_lowercase().contains(&search_term)
                || task.assignee.to_lowercase().contains(&search_term)
        });
    }

    // Sort by priority (Critical -> High -> Medium -> Low) then by created date
    tasks.sort_by(|a, b| {
        let priority_order = |p: Priority| match p {
            Priority::Critical => 0,
            Priority::High => 1,
            Priority::Medium => 2,
            Priority::Low => 3,
        };

        priority_order(a.priority)
            .cmp(&priority_order(b.priority))
            .then(a.created_at.cmp(&b.created_at))
    });

    tasks
}

/// Group tasks by their status for board view
fn group_tasks_by_status(state: &TaskState) -> HashMap<TaskStatus, Vec<&Task>> {
    let mut groups: HashMap<TaskStatus, Vec<&Task>> = HashMap::new();

    for task in state.tasks.values() {
        groups.entry(task.status).or_default().push(task);
    }

    groups
}

/// Get the previous task ID in the filtered list
fn get_previous_task_id(tasks: &[Task], current_id: u32) -> Option<u32> {
    let current_index = tasks.iter().position(|t| t.id == current_id)?;
    if current_index > 0 {
        Some(tasks[current_index - 1].id)
    } else {
        None
    }
}

/// Get the next task ID in the filtered list
fn get_next_task_id(tasks: &[Task], current_id: u32) -> Option<u32> {
    let current_index = tasks.iter().position(|t| t.id == current_id)?;
    if current_index < tasks.len() - 1 {
        Some(tasks[current_index + 1].id)
    } else {
        None
    }
}

/// Get the next status in the workflow
fn get_next_status(current: TaskStatus) -> TaskStatus {
    match current {
        TaskStatus::Todo => TaskStatus::InProgress,
        TaskStatus::InProgress => TaskStatus::Review,
        TaskStatus::Review => TaskStatus::Done,
        TaskStatus::Done => TaskStatus::Todo,
    }
}

/// Get the next priority level
fn get_next_priority(current: Priority) -> Priority {
    match current {
        Priority::Low => Priority::Medium,
        Priority::Medium => Priority::High,
        Priority::High => Priority::Critical,
        Priority::Critical => Priority::Low,
    }
}

/// Format timestamp for display
fn format_timestamp(timestamp: u64) -> String {
    // Simple timestamp formatting - in a real app you'd use chrono
    let days_ago = (SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs()
        - timestamp)
        / 86400;

    if days_ago == 0 {
        "Today".to_string()
    } else if days_ago == 1 {
        "Yesterday".to_string()
    } else {
        format!("{} days ago", days_ago)
    }
}
