//! Comprehensive use_effect hook examples
//!
//! This module demonstrates various patterns for the use_effect hook,
//! including effects with dependencies, cleanup functions, and side effects.

use crossterm::event::{Event, KeyCode};
use rink::prelude::*;
use rink_core::hooks::effect::use_effect;
use std::time::{SystemTime, UNIX_EPOCH};

/// Basic effect example that runs on every render
pub fn basic_effect_example() -> impl Into<VNode> {
    let (count, set_count) = use_state(0);
    let (log_messages, set_log_messages) = use_state(Vec::<String>::new());

    // Effect runs on every render (no dependencies)
    use_effect::<(), _, _>(
        {
            let count_value = count.get();
            let set_log_messages = set_log_messages.clone();
            move || {
                let timestamp = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();

                set_log_messages.update(|mut prev| {
                    prev.push(format!(
                        "Effect ran at {} with count: {}",
                        timestamp, count_value
                    ));
                    // Keep only last 5 messages
                    if prev.len() > 5 {
                        prev.remove(0);
                    }
                    prev
                });

                None::<Box<dyn FnOnce() + Send>> // No cleanup needed
            }
        },
        None, // No dependencies - runs on every render
    );

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char(' ') => {
                set_count.update(|prev| prev + 1);
            }
            KeyCode::Char('c') => {
                set_log_messages.set(Vec::new());
            }
            _ => {}
        }
    }

    let messages = log_messages.get();
    let log_text = if messages.is_empty() {
        "No log messages yet".to_string()
    } else {
        messages.join("\n")
    };

    rsx! {
        <Block
            title="Basic Effect Example (use_effect)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Count: {}\n\nEffect Log:\n{}\n\nControls:\nSpace : Increment count\nc : Clear log",
                    count.get(),
                    log_text
                )}
            </Paragraph>
        </Block>
    }
}

/// Effect with dependencies - only runs when dependencies change
pub fn effect_with_dependencies_example() -> impl Into<VNode> {
    let (user_id, set_user_id) = use_state(1);
    let (user_data, set_user_data) = use_state("Loading...".to_string());
    let (fetch_count, set_fetch_count) = use_state(0);

    // Effect runs only when user_id changes
    use_effect(
        {
            let user_id_value = user_id.get();
            let set_user_data = set_user_data.clone();
            let set_fetch_count = set_fetch_count.clone();
            move || {
                // Simulate data fetching
                let data = format!(
                    "User {} data loaded at {}",
                    user_id_value,
                    SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_secs()
                );
                set_user_data.set(data);
                set_fetch_count.update(|prev| prev + 1);

                None::<Box<dyn FnOnce() + Send>> // No cleanup needed
            }
        },
        user_id.get(), // Effect depends on user_id - only runs when it changes
    );

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('n') => {
                set_user_id.update(|prev| prev + 1);
            }
            KeyCode::Char('p') => {
                set_user_id.update(|prev| if prev > 1 { prev - 1 } else { 1 });
            }
            _ => {}
        }
    }

    rsx! {
        <Block
            title="Effect with Dependencies (use_effect)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Current User ID: {}\nFetch Count: {}\n\nUser Data:\n{}\n\nControls:\nn : Next user\np : Previous user",
                    user_id.get(),
                    fetch_count.get(),
                    user_data.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Effect with cleanup function
pub fn effect_with_cleanup_example() -> impl Into<VNode> {
    let (is_timer_active, set_is_timer_active) = use_state(false);
    let (timer_count, set_timer_count) = use_state(0);
    let (cleanup_count, set_cleanup_count) = use_state(0);

    // Effect with cleanup - simulates a timer
    use_effect(
        {
            let is_active = is_timer_active.get();
            let set_timer_count = set_timer_count.clone();
            let set_cleanup_count = set_cleanup_count.clone();
            move || {
                if is_active {
                    // Simulate starting a timer (in real app, this might be a thread or async task)
                    set_timer_count.update(|prev| prev + 1);

                    // Return cleanup function
                    Some(Box::new(move || {
                        set_cleanup_count.update(|prev| prev + 1);
                    }) as Box<dyn FnOnce() + Send>)
                } else {
                    None::<Box<dyn FnOnce() + Send>>
                }
            }
        },
        is_timer_active.get(), // Effect depends on timer state
    );

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('t') => {
                set_is_timer_active.update(|prev| !prev);
            }
            KeyCode::Char('r') => {
                set_timer_count.set(0);
                set_cleanup_count.set(0);
            }
            _ => {}
        }
    }

    let status = if is_timer_active.get() {
        "Active"
    } else {
        "Inactive"
    };

    rsx! {
        <Block
            title="Effect with Cleanup (use_effect)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Timer Status: {}\nTimer Count: {}\nCleanup Count: {}\n\nControls:\nt : Toggle timer\nr : Reset counters",
                    status,
                    timer_count.get(),
                    cleanup_count.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Complex effect with multiple dependencies
pub fn complex_effect_example() -> impl Into<VNode> {
    let (name, set_name) = use_state("John".to_string());
    let (age, set_age) = use_state(25);
    let (profile_summary, set_profile_summary) = use_state("".to_string());
    let (update_count, set_update_count) = use_state(0);

    // Effect depends on both name and age
    use_effect(
        {
            let name_value = name.get();
            let age_value = age.get();
            let set_profile_summary = set_profile_summary.clone();
            let set_update_count = set_update_count.clone();
            move || {
                let summary = format!(
                    "Profile: {} is {} years old. Generated at {}",
                    name_value,
                    age_value,
                    SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_secs()
                );
                set_profile_summary.set(summary);
                set_update_count.update(|prev| prev + 1);

                None::<Box<dyn FnOnce() + Send>>
            }
        },
        (name.get(), age.get()), // Effect depends on both name and age
    );

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('n') => {
                let names = ["John", "Jane", "Bob", "Alice", "Charlie"];
                let current_name = name.get();
                let current_index = names.iter().position(|&n| n == current_name).unwrap_or(0);
                let next_index = (current_index + 1) % names.len();
                set_name.set(names[next_index].to_string());
            }
            KeyCode::Char('a') => {
                set_age.update(|prev| if prev >= 60 { 18 } else { prev + 5 });
            }
            _ => {}
        }
    }

    rsx! {
        <Block
            title="Complex Effect with Multiple Dependencies (use_effect)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Name: {}\nAge: {}\nUpdate Count: {}\n\nProfile Summary:\n{}\n\nControls:\nn : Change name\na : Change age",
                    name.get(),
                    age.get(),
                    update_count.get(),
                    profile_summary.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Main function to run all use_effect examples
pub fn run_use_effect_examples() -> Result<(), Box<dyn std::error::Error>> {
    println!("Running use_effect examples...");
    println!("Press 'q' to quit any example");

    // You can uncomment any of these to run individual examples:
    // render(basic_effect_example)?;
    // render(effect_with_dependencies_example)?;
    // render(effect_with_cleanup_example)?;
    render(complex_effect_example)?;

    Ok(())
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    run_use_effect_examples()
}
