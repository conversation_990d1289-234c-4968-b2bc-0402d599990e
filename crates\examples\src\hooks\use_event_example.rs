//! Comprehensive use_event hook examples
//!
//! This module demonstrates various patterns for the use_event hook,
//! including keyboard handling, mouse events, resize events, and complex event processing.

use crossterm::event::{Event, KeyCode, KeyModifiers, MouseButton, MouseEventKind};
use rink::prelude::*;

/// Basic keyboard event handling example
pub fn keyboard_events_example() -> impl Into<VNode> {
    let (last_key, set_last_key) = use_state("None".to_string());
    let (key_count, set_key_count) = use_state(0);
    let (modifiers, set_modifiers) = use_state("None".to_string());

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        let key_info = match key.code {
            KeyCode::Char(c) => format!("Char('{}')", c),
            KeyCode::Enter => "Enter".to_string(),
            KeyCode::Esc => "Escape".to_string(),
            KeyCode::Backspace => "Backspace".to_string(),
            KeyCode::Delete => "Delete".to_string(),
            KeyCode::Tab => "Tab".to_string(),
            KeyCode::Up => "Up Arrow".to_string(),
            KeyCode::Down => "Down Arrow".to_string(),
            KeyCode::Left => "Left Arrow".to_string(),
            KeyCode::Right => "Right Arrow".to_string(),
            KeyCode::Home => "Home".to_string(),
            KeyCode::End => "End".to_string(),
            KeyCode::PageUp => "Page Up".to_string(),
            KeyCode::PageDown => "Page Down".to_string(),
            KeyCode::F(n) => format!("F{}", n),
            _ => format!("{:?}", key.code),
        };

        set_last_key.set(key_info);
        set_key_count.update(|prev| prev + 1);

        // Handle modifiers
        let mut mod_list = Vec::new();
        if key.modifiers.contains(KeyModifiers::CONTROL) {
            mod_list.push("Ctrl");
        }
        if key.modifiers.contains(KeyModifiers::ALT) {
            mod_list.push("Alt");
        }
        if key.modifiers.contains(KeyModifiers::SHIFT) {
            mod_list.push("Shift");
        }

        let modifier_text = if mod_list.is_empty() {
            "None".to_string()
        } else {
            mod_list.join(" + ")
        };

        set_modifiers.set(modifier_text);
    }

    rsx! {
        <Block
            title="Keyboard Events (use_event)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Last Key: {}\nModifiers: {}\nTotal Keys Pressed: {}\n\nTry different keys:\n- Letters and numbers\n- Arrow keys\n- Function keys (F1-F12)\n- Ctrl/Alt/Shift combinations\n- Special keys (Enter, Esc, etc.)",
                    last_key.get(),
                    modifiers.get(),
                    key_count.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Mouse event handling example
pub fn mouse_events_example() -> impl Into<VNode> {
    let (last_mouse_event, set_last_mouse_event) = use_state("None".to_string());
    let (mouse_position, set_mouse_position) = use_state((0u16, 0u16));
    let (click_count, set_click_count) = use_state(0);
    let (drag_info, set_drag_info) = use_state("Not dragging".to_string());

    // Handle mouse events
    if let Some(Event::Mouse(mouse)) = use_event() {
        set_mouse_position.set((mouse.column, mouse.row));

        let event_description = match mouse.kind {
            MouseEventKind::Down(button) => {
                let button_name = match button {
                    MouseButton::Left => "Left",
                    MouseButton::Right => "Right",
                    MouseButton::Middle => "Middle",
                };
                set_click_count.update(|prev| prev + 1);
                format!("{} Button Down", button_name)
            }
            MouseEventKind::Up(button) => {
                let button_name = match button {
                    MouseButton::Left => "Left",
                    MouseButton::Right => "Right",
                    MouseButton::Middle => "Middle",
                };
                format!("{} Button Up", button_name)
            }
            MouseEventKind::Drag(button) => {
                let button_name = match button {
                    MouseButton::Left => "Left",
                    MouseButton::Right => "Right",
                    MouseButton::Middle => "Middle",
                };
                set_drag_info.set(format!("Dragging with {} button", button_name));
                format!("{} Button Drag", button_name)
            }
            MouseEventKind::Moved => {
                set_drag_info.set("Mouse moved".to_string());
                "Mouse Moved".to_string()
            }
            MouseEventKind::ScrollDown => "Scroll Down".to_string(),
            MouseEventKind::ScrollUp => "Scroll Up".to_string(),
            MouseEventKind::ScrollLeft => "Scroll Left".to_string(),
            MouseEventKind::ScrollRight => "Scroll Right".to_string(),
        };

        set_last_mouse_event.set(event_description);
    }

    rsx! {
        <Block
            title="Mouse Events (use_event)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Last Event: {}\nMouse Position: ({}, {})\nTotal Clicks: {}\nDrag Info: {}\n\nTry different mouse actions:\n- Left/Right/Middle clicks\n- Mouse movement\n- Dragging\n- Scroll wheel",
                    last_mouse_event.get(),
                    mouse_position.get().0,
                    mouse_position.get().1,
                    click_count.get(),
                    drag_info.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Terminal resize event handling example
pub fn resize_events_example() -> impl Into<VNode> {
    let (terminal_size, set_terminal_size) = use_state((0u16, 0u16));
    let (resize_count, set_resize_count) = use_state(0);
    let (last_resize_time, set_last_resize_time) = use_state("Never".to_string());

    // Handle resize events
    if let Some(Event::Resize(width, height)) = use_event() {
        set_terminal_size.set((width, height));
        set_resize_count.update(|prev| prev + 1);

        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        set_last_resize_time.set(format!("Timestamp: {}", timestamp));
    }

    let (width, height) = terminal_size.get();
    let area = width as u32 * height as u32;

    rsx! {
        <Block
            title="Resize Events (use_event)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Terminal Size: {}x{}\nTotal Area: {} cells\nResize Count: {}\nLast Resize: {}\n\nTry resizing your terminal window\nto see this information update",
                    width,
                    height,
                    area,
                    resize_count.get(),
                    last_resize_time.get()
                )}
            </Paragraph>
        </Block>
    }
}

/// Complex event handling with state management
pub fn complex_event_handling_example() -> impl Into<VNode> {
    let (mode, set_mode) = use_state("Normal".to_string());
    let (input_buffer, set_input_buffer) = use_state(String::new());
    let (cursor_pos, set_cursor_pos) = use_state((0u16, 0u16));
    let (event_log, set_event_log) = use_state(Vec::<String>::new());

    // Handle all types of events
    if let Some(event) = use_event() {
        let event_description = match &event {
            Event::Key(key) => match key.code {
                KeyCode::Char('i') if mode.get() == "Normal" => {
                    set_mode.set("Insert".to_string());
                    "Entered Insert Mode".to_string()
                }
                KeyCode::Esc if mode.get() == "Insert" => {
                    set_mode.set("Normal".to_string());
                    "Entered Normal Mode".to_string()
                }
                KeyCode::Char(c) if mode.get() == "Insert" => {
                    set_input_buffer.update(|mut prev| {
                        prev.push(c);
                        prev
                    });
                    format!("Typed: '{}'", c)
                }
                KeyCode::Backspace if mode.get() == "Insert" => {
                    set_input_buffer.update(|mut prev| {
                        prev.pop();
                        prev
                    });
                    "Backspace".to_string()
                }
                KeyCode::Enter if mode.get() == "Insert" => {
                    let buffer = input_buffer.get();
                    set_input_buffer.set(String::new());
                    format!("Submitted: '{}'", buffer)
                }
                _ => format!("Key: {:?}", key.code),
            },
            Event::Mouse(mouse) => {
                set_cursor_pos.set((mouse.column, mouse.row));
                format!(
                    "Mouse: {:?} at ({}, {})",
                    mouse.kind, mouse.column, mouse.row
                )
            }
            Event::Resize(w, h) => {
                format!("Resized to {}x{}", w, h)
            }
            Event::FocusGained => "Focus Gained".to_string(),
            Event::FocusLost => "Focus Lost".to_string(),
            Event::Paste(text) => format!("Pasted: '{}'", text),
        };

        // Add to event log
        set_event_log.update(|mut prev| {
            prev.push(event_description);
            // Keep only last 8 events
            if prev.len() > 8 {
                prev.remove(0);
            }
            prev
        });
    }

    let log_display = event_log.get().join("\n");
    let mode_color = if mode.get() == "Insert" {
        Color::Green
    } else {
        Color::Blue
    };

    rsx! {
        <Block
            title={format!("Complex Event Handler - Mode: {}", mode.get())}
            borders={Borders::ALL}
            border_style={Style::default().fg(mode_color)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Current Mode: {}\nInput Buffer: '{}'\nCursor Position: ({}, {})\n\nRecent Events:\n{}\n\nControls:\ni : Enter Insert mode\nEsc : Enter Normal mode\nType in Insert mode",
                    mode.get(),
                    input_buffer.get(),
                    cursor_pos.get().0,
                    cursor_pos.get().1,
                    log_display
                )}
            </Paragraph>
        </Block>
    }
}

/// Event statistics and analysis example
pub fn event_statistics_example() -> impl Into<VNode> {
    let (key_stats, set_key_stats) = use_state((0, 0, 0)); // (total, letters, special)
    let (mouse_stats, set_mouse_stats) = use_state((0, 0, 0)); // (total, clicks, moves)
    let (other_stats, set_other_stats) = use_state(0); // resize, focus, etc.

    // Analyze and categorize events
    if let Some(event) = use_event() {
        match event {
            Event::Key(key) => {
                set_key_stats.update(|(total, letters, special)| {
                    let new_total = total + 1;
                    match key.code {
                        KeyCode::Char(c) if c.is_alphabetic() => (new_total, letters + 1, special),
                        KeyCode::Char(_) => (new_total, letters, special + 1),
                        _ => (new_total, letters, special + 1),
                    }
                });
            }
            Event::Mouse(mouse) => {
                set_mouse_stats.update(|(total, clicks, moves)| {
                    let new_total = total + 1;
                    match mouse.kind {
                        MouseEventKind::Down(_) | MouseEventKind::Up(_) => {
                            (new_total, clicks + 1, moves)
                        }
                        MouseEventKind::Moved | MouseEventKind::Drag(_) => {
                            (new_total, clicks, moves + 1)
                        }
                        _ => (new_total, clicks, moves),
                    }
                });
            }
            _ => {
                set_other_stats.update(|prev| prev + 1);
            }
        }
    }

    let (key_total, key_letters, key_special) = key_stats.get();
    let (mouse_total, mouse_clicks, mouse_moves) = mouse_stats.get();
    let other_total = other_stats.get();
    let grand_total = key_total + mouse_total + other_total;

    rsx! {
        <Block
            title="Event Statistics (use_event)"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Left}
            >
                {format!(
                    "Total Events: {}\n\nKeyboard Events: {}\n  - Letters: {}\n  - Special Keys: {}\n\nMouse Events: {}\n  - Clicks: {}\n  - Movements: {}\n\nOther Events: {}\n\nInteract with the terminal to see\nstatistics update in real-time",
                    grand_total,
                    key_total,
                    key_letters,
                    key_special,
                    mouse_total,
                    mouse_clicks,
                    mouse_moves,
                    other_total
                )}
            </Paragraph>
        </Block>
    }
}

/// Main function to run all use_event examples
pub fn run_use_event_examples() -> Result<(), Box<dyn std::error::Error>> {
    println!("Running use_event examples...");
    println!("Press 'q' to quit any example");

    // You can uncomment any of these to run individual examples:
    // render(keyboard_events_example)?;
    // render(mouse_events_example)?;
    // render(resize_events_example)?;
    // render(complex_event_handling_example)?;
    render(event_statistics_example)?;

    Ok(())
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    run_use_event_examples()
}
